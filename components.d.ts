/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChatHistory: typeof import('./src/components/ChatHistory/index.vue')['default']
    Compass: typeof import('./src/components/Compass/index.vue')['default']
    DebugTest: typeof import('./src/components/FloorPanel/DebugTest.vue')['default']
    EventBusControlTest: typeof import('./src/components/FloorPanel/EventBusControlTest.vue')['default']
    EventBusTest: typeof import('./src/components/FloorPanel/EventBusTest.vue')['default']
    ExplodedModeTest: typeof import('./src/components/FloorPanel/ExplodedModeTest.vue')['default']
    FloorPanel: typeof import('./src/components/FloorPanel/index.vue')['default']
    FloorPanelExample: typeof import('./src/components/FloorPanel/FloorPanelExample.vue')['default']
    FloorSelector: typeof import('./src/components/FloorSelector.vue')['default']
    IntegrationTest: typeof import('./src/components/FloorPanel/IntegrationTest.vue')['default']
    LayerManagerPanel: typeof import('./src/components/LayerManagerPanel.vue')['default']
    LoadingOverlay: typeof import('./src/components/LoadingOverlay.vue')['default']
    LODIntegrationDemo: typeof import('./src/components/FloorPanel/LODIntegrationDemo.vue')['default']
    LODIntegrationExample: typeof import('./src/components/FloorPanel/LODIntegrationExample.vue')['default']
    MapContainer: typeof import('./src/components/MapContainer.vue')['default']
    MapViewer: typeof import('./src/components/MapViewer.vue')['default']
    PassengerFlow: typeof import('./src/components/PassengerFlow/index.vue')['default']
    Progress: typeof import('./src/components/Progress/index.vue')['default']
    ProperIntegrationExample: typeof import('./src/components/FloorPanel/ProperIntegrationExample.vue')['default']
    TimeControlPanel: typeof import('./src/components/TimeControlPanel/index.vue')['default']
    UnifiedEventBusTest: typeof import('./src/components/FloorPanel/UnifiedEventBusTest.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanImage: typeof import('vant/es')['Image']
    VanInput: typeof import('vant/es')['Input']
    VanPicker: typeof import('vant/es')['Picker']
    VanSearch: typeof import('vant/es')['Search']
  }
}
