# 湖北省博物馆3D数字导览系统

一个基于 Vue 3 + Three.js + Mapbox 的沉浸式3D博物馆数字导览系统，为湖北省博物馆提供高精度的室内外一体化3D可视化体验。

## 🏛️ 项目概述

本系统专为湖北省博物馆设计，提供完整的3D数字导览解决方案。系统基于真实的地理坐标（114.359°E, 30.564°N），支持从园区鸟瞰到室内细节的无缝缩放，为参观者提供沉浸式的数字化参观体验。

### 核心特色
- 🎯 **高精度定位** - 基于真实地理坐标的精确3D建模
- 🏗️ **室内外一体** - 建筑外观与室内楼层的无缝切换
- 🚀 **性能优化** - 智能LOD系统，支持大规模3D场景
- 📱 **多端适配** - 完美支持桌面和移动设备
- ⏰ **实时环境** - 真实的光照和时间变化模拟

## ✨ 功能特性

### 🎮 3D可视化核心
- **混合渲染引擎** - Mapbox矢量地图 + Three.js 3D场景
- **双馆建筑模型** - 东馆、西馆独立3D建筑
- **多楼层支持** - 每个建筑支持多个楼层展示
- **高质量渲染** - 60FPS流畅渲染，支持纹理贴图

### 🎛️ 智能交互控制
- **楼层控制系统** - 直观的滚动式楼层选择器
- **拆楼模式** - 建筑分解展示，便于查看内部结构
- **指南针导航** - 实时方向指示和视角控制
- **点击交互** - 精确的3D射线检测和坐标显示

### 🚀 性能优化系统
- **4级LOD系统** - 距离感知的模型精度自动调整
  - 🔴 高精度 (50m内) - 完整细节模型
  - 🟡 中精度 (200m内) - 简化模型
  - 🟢 低精度 (500m内) - 低面数模型
  - ⚫ 隐藏 (1000m外) - 完全隐藏
- **智能资源管理** - 按需加载，自动清理
- **性能监控** - 实时FPS、内存使用统计

### 📍 3D标注系统
- **多类型标注** - 点、线、面三种标注类型
- **文物定位** - 标记重要文物、展厅、设施位置
- **实时编辑** - 支持标注属性的实时修改
- **数据导出** - 标准GeoJSON格式，兼容GIS系统
- **坐标转换** - 3D世界坐标与地理坐标精确转换

### ⏰ 时间环境系统
- **实时光照模拟** - 基于真实时间的太阳位置计算
- **动态天空** - 实时天空颜色和亮度变化
- **时间控制** - 支持时间设置、倍速播放等功能
- **环境同步** - 光照、阴影与时间实时同步

## 🛠️ 技术架构

### 技术栈
- **前端框架**: Vue 3 + TypeScript + Composition API
- **3D渲染**: Three.js (0.178.0) - WebGL 3D图形库
- **地图引擎**: Mapbox GL JS (3.13.0) - 矢量地图渲染
- **UI组件**: Vant 4 - 移动端UI组件库
- **动画库**: GSAP (3.13.0) - 高性能动画库
- **构建工具**: Vite 7 + Vue-tsc - 现代化构建工具链

### 核心架构
```
┌─────────────────────────────────────┐
│           Vue 3 组件层               │  UI交互层
├─────────────────────────────────────┤
│           Hook 抽象层               │  业务逻辑层
├─────────────────────────────────────┤
│           管理器层                  │  功能管理层
├─────────────────────────────────────┤
│        Mapbox + Three.js 渲染层     │  渲染引擎层
└─────────────────────────────────────┘
```

### 主要管理器
- **MapApplication** - 核心应用类，地图和3D场景管理
- **LayerManager** - 图层管理器，3D模型加载和管理
- **DefaultLODManager** - LOD管理器，性能优化和楼层控制
- **Annotation3DManager** - 3D标注管理器，标注系统
- **TimeSystem** - 时间系统，环境光照控制
- **RenderManager** - 渲染管理器，渲染循环优化

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- pnpm >= 7.0.0

### 安装依赖
```bash
# 使用 pnpm 安装依赖
pnpm install
```

### 开发模式
```bash
# 启动开发服务器
pnpm dev

# 访问 http://localhost:5173
```

### 生产构建
```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

## 📁 项目结构

```
省博-20250808/
├── public/                          # 静态资源
│   ├── models/                      # 3D模型文件
│   ├── texture/                     # 纹理贴图
│   ├── icon/                        # 图标资源
│   ├── modelList.json              # 模型配置数据
│   └── markers.geojson             # 地理标记数据
├── src/
│   ├── components/                  # Vue组件
│   │   ├── Compass/                # 指南针组件
│   │   ├── FloorPanel/             # 楼层控制面板
│   │   ├── TimeControlPanel/       # 时间控制面板
│   │   └── LoadingOverlay.vue      # 加载进度组件
│   ├── hooks/                       # Vue Hooks
│   │   └── useRefactoredMapViewer.ts # 主要业务逻辑Hook
│   ├── refactored/                  # 核心管理器
│   │   ├── MapApplication.ts        # 地图应用主类
│   │   ├── LayerManager.ts         # 图层管理器
│   │   ├── DefaultLODManager.ts    # LOD管理器
│   │   ├── Annotation3DManager.ts  # 3D标注管理器
│   │   ├── TimeSystem.ts           # 时间系统
│   │   ├── RenderManager.ts        # 渲染管理器
│   │   ├── ModelTreeManager.ts     # 模型树管理器
│   │   ├── EventBus.ts             # 事件总线
│   │   └── UnifiedConfig.ts        # 统一配置
│   ├── page/
│   │   └── index.vue               # 主页面组件
│   └── App.vue                     # 应用根组件
├── package.json                     # 项目配置
└── vite.config.ts                  # Vite配置
```

## 🎯 使用指南

### 基本操作

#### 地图导航
- **鼠标拖拽** - 平移地图视角
- **鼠标滚轮** - 缩放地图
- **右键拖拽** - 旋转视角
- **指南针** - 点击重置方向

#### 楼层控制
1. 使用右侧楼层面板选择楼层
2. 滚动选择目标楼层
3. 点击拆楼按钮查看建筑内部结构
4. 支持触摸设备的手势操作

#### 3D标注功能
1. **添加点标注** - 点击场景中的位置添加标记
2. **添加线标注** - 连续点击绘制路径线
3. **添加面标注** - 绘制封闭区域
4. **编辑标注** - 修改名称、颜色等属性
5. **导出数据** - 导出为GeoJSON格式

#### 时间控制
1. 展开时间控制面板
2. 设置目标时间
3. 选择播放倍速
4. 观察光照和阴影变化

### 高级功能

#### LOD性能优化
- 系统自动根据距离调整模型精度
- 可在设置中调整LOD距离阈值
- 实时监控性能指标

#### 坐标系统
- **3D世界坐标** - Three.js场景坐标
- **地理坐标** - WGS84经纬度坐标
- **一键复制** - 复制坐标到剪贴板
- **地图查看** - 在Google Maps中查看位置

## 🔧 开发指南

### 添加新的3D模型
1. 将OBJ/MTL文件放入 `public/models/` 目录
2. 在 `public/modelList.json` 中添加模型配置
3. 重启开发服务器

### 自定义配置
编辑 `src/refactored/UnifiedConfig.ts` 文件：
```typescript
export const SCENE_CONFIG = {
  MAP_CENTER: [114.35962345673624, 30.564406727759334], // 地图中心
  DEFAULT_ZOOM: 18,                                     // 默认缩放
  MODEL_ALTITUDE: 0,                                    // 模型海拔
}
```

### 扩展功能模块
1. 在 `src/refactored/` 目录下创建新的管理器类
2. 在 `src/hooks/useRefactoredMapViewer.ts` 中集成
3. 在组件中使用Hook提供的接口

## 📊 性能优化

### LOD系统配置
```typescript
const LOD_CONFIG = {
  distances: {
    high: 50,      // 50米内显示高精度
    medium: 200,   // 200米内显示中精度
    low: 500,      // 500米内显示低精度
    hidden: 1000   // 1000米外隐藏
  }
}
```

### 渲染优化建议
- 合理设置LOD距离阈值
- 及时清理不需要的3D对象
- 使用纹理压缩减少内存占用
- 启用性能监控查看实时指标

## 🌍 坐标系统说明

### 支持的坐标系
- **WGS84地理坐标系** - 标准经纬度坐标
- **Web墨卡托投影** - Mapbox地图坐标系
- **Three.js世界坐标系** - 3D场景坐标系

### 坐标转换
系统提供精确的坐标转换功能：
```typescript
// 3D坐标转地理坐标
const geoCoord = coordinateConverter.worldToGeo(worldPosition)

// 地理坐标转3D坐标
const worldCoord = coordinateConverter.geoToWorld(longitude, latitude)
```

## 🎨 标注数据格式

### GeoJSON导出格式
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [114.359623, 30.564407]
      },
      "properties": {
        "name": "重要位置",
        "category": "文物",
        "description": "重要文物展品位置"
      }
    }
  ],
  "metadata": {
    "exportTime": "2025-01-08T12:00:00.000Z",
    "coordinateSystem": "WGS84",
    "source": "湖北省博物馆3D导览系统"
  }
}
```

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 Composition API 最佳实践
- 保持代码注释的完整性
- 确保新功能有对应的文档说明

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Three.js](https://threejs.org/) - JavaScript 3D库
- [Mapbox GL JS](https://docs.mapbox.com/mapbox-gl-js/) - 交互式矢量地图
- [Vant](https://vant-ui.github.io/vant/) - 移动端Vue组件库

---

**湖北省博物馆3D数字导览系统** - 让文物在数字世界中重新焕发生机 ✨
