<template>
    <div class="map-viewer">
        <!-- 地图容器 -->
        <div ref="mapContainer" class="map-container"></div>

        <!-- 加载进度 -->
        <LoadingOverlay :visible="isLoading" :text="loadingText" :progress="loadingProgress" />

        <div class="floating-ui" v-if="!isLoading">
            <!-- 指南针 -->
            <Compass></Compass>

            <!-- 楼层选择器 - 完整LOD集成 -->
            <FloorPanel :auto-sync="true" :show-lod-status="true" :building-current-floors="buildingCurrentFloors"
                @lod-floor-change="onLODFloorChange" @floor-switch="onFloorSwitch"
                @exploded-mode-toggle="onExplodedModeToggle" />

            <!-- 时间控制面板 -->
            <TimeControlPanel 
                :getCurrentTime="getCurrentTime"
                :setCurrentTime="setCurrentTime"
                :startTimeSystem="startTimeSystem"
                :stopTimeSystem="stopTimeSystem"
                :setTimeScale="setTimeScale"
                :getTimeScale="getTimeScale"
            />
        </div>

        <!-- 紧凑控制面板 -->
        <!-- <CompactControlPanel v-if="!isLoading" :models="modelList" :buildings="buildingList" :stats="stats"
            :lodManager="lodManager" @load-all="loadAllModels" @show-all="showAllModels" @hide-all="hideAllModels"
            @reset-view="resetView" @load-model="loadModel" @toggle-visibility="toggleVisibility"
            @switch-floor="switchFloor" /> -->

        <!-- 点击信息显示 -->
        <!-- <ClickInfo :click-data="clickInfo" @close="closeClickInfo" /> -->

        <!-- 室内建筑信息面板 -->
        <!-- <InteriorBuildingInfo :lodManager="lodManager" @floorChange="handleFloorChange"
            @configChange="handleLODConfigChange" /> -->

        <!-- 3D标注面板 -->
        <!-- <Annotation3DPanel v-if="!isLoading && annotationManager" :annotationManager="annotationManager" /> -->
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, provide } from 'vue'
import { useRefactoredMapViewer } from '@/hooks/useRefactoredMapViewer'

// 导入组件
import LoadingOverlay from '@/components/LoadingOverlay.vue'
import Compass from '@/components/Compass/index.vue'
import FloorPanel from '@/components/FloorPanel/index.vue'
import TimeControlPanel from '@/components/TimeControlPanel/index.vue'

const mapContainer = ref<HTMLElement>()

// 使用封装的 hook
const {
    // 响应式状态
    isLoading,
    loadingText,
    loadingProgress,
    clickInfo,

    // 计算属性
    modelList,
    buildingList,
    stats,

    // 管理器实例
    lodManager,

    // 楼层状态
    buildingCurrentFloors,
    initTestBeaconLocation,
    updateUserLocation,
    // 方法
    switchFloor,
    handleFloorChange,
    resetView,
    initializeApp,
    setupEventListeners,
    
    // 时间系统控制方法
    getCurrentTime,
    setCurrentTime,
    startTimeSystem,
    stopTimeSystem,
    setTimeScale,
    getTimeScale
} = useRefactoredMapViewer({
    defaultCenter: [114.35962345673624, 30.564406727759334],
    defaultZoom: 18,
})

// LOD楼层变更处理
const onLODFloorChange = (data: { buildingId: string, floor: number }) => {
    console.log('🏢 页面接收到LOD楼层变更:', data)
    // 调用hook中的楼层切换方法
    handleFloorChange(data.buildingId, data.floor)
}

// 楼层切换处理
const onFloorSwitch = (data: { buildingId: string, floor: number }) => {
    console.log('🎮 页面接收到楼层切换请求:', data)
    // 调用hook中的楼层切换方法
    switchFloor(data.buildingId, data.floor)
}

// 拆楼模式切换处理
const onExplodedModeToggle = (data: { buildingId: string, enabled: boolean }) => {
    console.log('🏗️ 页面接收到拆楼模式切换:', data)
    // 调用hook中的拆楼模式切换方法
    if (lodManager.value && typeof (lodManager.value as any).setExplodedMode === 'function') {
        (lodManager.value as any).setExplodedMode(data.buildingId, data.enabled)
    }
}

// 提供LOD管理器给子组件
provide('lodManager', lodManager)

// 生命周期
onMounted(async () => {
    setupEventListeners()
    await nextTick()
    // 等待容器元素准备好，然后初始化
    if (mapContainer.value) {
        await initializeApp(mapContainer.value)
        // window.addEventListener('hashchange', hashChange)
        // initTestBeaconLocation();
    }
})

// URL变化处理函数
// const hashChange = () => {
//     const urlData = parseURLParams()
//     // 更新用户位置（信标定位）
//     if (urlData.x !== undefined && urlData.y !== undefined) {
//         updateUserLocation(urlData.x, urlData.y, 1);
//     }
// }

// 解析URL参数 (兼容方式)
// function parseURLParams(): any {
//     const result: any = {}
//     let queryString = window.location.hash.split('?')[1]
//     console.log(queryString);
//     if (!queryString) {
//         return result
//     }
//     // 尝试使用URLSearchParams (现代浏览器)
//     const params = new URLSearchParams(queryString)
//     // 解析位置参数
//     if (params.has('x')) result.x = parseFloat(params.get('x') || '0')
//     if (params.has('y')) result.y = parseFloat(params.get('y') || '0')
//     if (params.has('confidence')) result.confidence = parseFloat(params.get('confidence') || '0')
//     return result
// }


onUnmounted(() => {
    // 清理资源
    lodManager.value?.dispose()
})
</script>

<style scoped>
.map-viewer {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.map-container {
    width: 100%;
    height: 100%;
}

.floating-ui {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 9;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    pointer-events: none;
    /* 允许点击穿透到地图 */
}

.floating-ui>* {
    pointer-events: auto;
    /* 恢复子元素的点击事件 */
}
</style>