<template>
	<div class="compass" @pointerdown="resetRotate">
		<van-image class="pointer" :src="pointerUrl"> </van-image>
		<span
			v-for="i in directionList"
			:class="{
				[i.id]: true,
				active: activeDirectionId === i.id,
			}"
			:key="i.id"
		>
			{{ i.name }}
		</span>
	</div>
</template>

<script setup lang="ts">
import pointerUrl from "@/assets/image/pointer.svg"
import { eventBus } from "@/refactored/EventBus"
import { computed, onUnmounted, ref } from "vue"
const bearingRef = ref(0)

const directionList = [
	{
		id: "east",
		name: "东",
		angle: 90,
	},
	{
		id: "south",
		name: "南",
		angle: 180,
	},
	{
		id: "west",
		name: "西",
		angle: -90,
	},
	{
		id: "north",
		name: "北",
		angle: 0,
	},
]

const activeDirectionId = computed(() => {
	return directionList.find((i) => {
		return Math.abs(i.angle - bearingRef.value) < 22.5
	})?.id
})

const updateRotate = (event) => {
	bearingRef.value = event.target.getBearing()
}

const resetRotate = () => {
	eventBus.emit("reset-rotate")
}

eventBus.on("rotate", updateRotate)

onUnmounted(() => {
	eventBus.off("rotate", updateRotate)
})
</script>

<style scoped lang="scss">
.compass {
	width: 54px;
	height: 54px;
	// position: absolute;
	// top: 10px;
	// left: 10px;
	display: flex;
	justify-content: center;
	align-items: center;
	transform: rotate(calc(v-bind(bearingRef) * 1deg));
	background: url(@/assets/image/compass.svg) no-repeat center / cover;
	.pointer {
		width: 8.6px;
		height: 30.24px;
		
	}
	> span {
		position: absolute;
		color: #a57a4b;
		font-size: 6.5px;
		top: 0;
		display: inline-block;
		text-align: center;
		width: 12px;
		height: 13px;
		line-height: 13px;
		&.active {
			color: rgba(172, 57, 30, 1);
		}
	}
	.east {
		right: 0;
		top: 50%;
		transform: translate(0, -50%);
	}
	> .south {
		bottom: 0;
		left: 50%;
		transform: translate(-50%, 0);
		top: unset;
	}
	.west {
		left: 0;
		top: 50%;
		transform: translate(0, -50%);
	}
}
</style>
