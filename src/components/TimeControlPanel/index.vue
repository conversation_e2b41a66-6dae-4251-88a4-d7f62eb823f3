<template>
  <div class="time-control-panel">
    <div class="panel-header">
      <h3>时间控制</h3>
      <button @click="togglePanel" class="toggle-btn">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-if="isExpanded" class="panel-content">
      <!-- 当前时间显示 -->
      <div class="time-display">
        <div class="current-time">
          <span class="label">当前时间:</span>
          <span class="time">{{ formatTime(currentTime) }}</span>
        </div>
        <div class="sun-info">
          <span class="label">太阳高度角:</span>
          <span class="value">{{ sunElevation.toFixed(1) }}°</span>
        </div>
      </div>

      <!-- 时间控制 -->
      <div class="time-controls">
        <div class="control-group">
          <label>设置时间:</label>
          <input 
            type="datetime-local" 
            :value="formatDateTimeLocal(currentTime)"
            @change="onTimeChange"
            class="time-input"
          />
        </div>

        <div class="control-group">
          <label>时间倍速:</label>
          <div class="speed-controls">
            <button 
              v-for="speed in speedOptions" 
              :key="speed.value"
              @click="setTimeScale(speed.value)"
              :class="{ active: timeScale === speed.value }"
              class="speed-btn"
            >
              {{ speed.label }}
            </button>
          </div>
        </div>

        <div class="control-group">
          <label>快速设置:</label>
          <div class="quick-time-controls">
            <button 
              v-for="preset in timePresets"
              :key="preset.label"
              @click="setQuickTime(preset.hour)"
              class="preset-btn"
            >
              {{ preset.label }}
            </button>
          </div>
        </div>

        <div class="control-group">
          <div class="play-controls">
            <button 
              @click="toggleTimeSystem"
              :class="{ active: isTimeSystemRunning }"
              class="play-btn"
            >
              {{ isTimeSystemRunning ? '暂停' : '播放' }}
            </button>
            <button @click="resetToCurrentTime" class="reset-btn">
              重置到当前时间
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  getCurrentTime: () => Date
  setCurrentTime: (time: Date) => void
  startTimeSystem: () => void
  stopTimeSystem: () => void
  setTimeScale: (scale: number) => void
  getTimeScale: () => number
}

const props = defineProps<Props>()

const isExpanded = ref(true)
const isTimeSystemRunning = ref(true)
const currentTime = ref(new Date())
const timeScale = ref(1) // 🎯 默认1倍速

// 时间倍速选项
const speedOptions = [
  { label: '1x', value: 1 },
  { label: '10x', value: 10 },
  { label: '60x', value: 60 },
  { label: '300x', value: 300 },
  { label: '1440x', value: 1440 }, // 1天/分钟
]

// 时间预设
const timePresets = [
  { label: '日出', hour: 6 },
  { label: '上午', hour: 9 },
  { label: '正午', hour: 12 },
  { label: '下午', hour: 15 },
  { label: '日落', hour: 18 },
  { label: '夜晚', hour: 21 },
]

// 计算太阳高度角（简化计算）
const sunElevation = computed(() => {
  const hour = currentTime.value.getHours()
  const minute = currentTime.value.getMinutes()
  const timeInHours = hour + minute / 60
  
  // 简化的太阳高度角计算（假设正午12点太阳最高90度）
  const solarNoon = 12
  const maxElevation = 60 // 武汉地区夏季最大太阳高度角约60度
  
  if (timeInHours >= 6 && timeInHours <= 18) {
    // 白天：使用正弦函数模拟太阳轨迹
    const hoursSinceSunrise = timeInHours - 6
    const dayLength = 12
    const angle = (hoursSinceSunrise / dayLength) * Math.PI
    return Math.sin(angle) * maxElevation
  } else {
    // 夜晚：太阳在地平线以下
    return -10
  }
})

// 格式化时间显示
const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化为datetime-local输入格式
const formatDateTimeLocal = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day}T${hours}:${minutes}`
}

// 切换面板展开状态
const togglePanel = () => {
  isExpanded.value = !isExpanded.value
}

// 时间变化处理
const onTimeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const newTime = new Date(target.value)
  if (!isNaN(newTime.getTime())) {
    props.setCurrentTime(newTime)
    currentTime.value = newTime
  }
}

// 设置时间倍速
const setTimeScale = (scale: number) => {
  props.setTimeScale(scale)
  timeScale.value = scale
}

// 快速设置时间
const setQuickTime = (hour: number) => {
  const newTime = new Date(currentTime.value)
  newTime.setHours(hour, 0, 0, 0)
  props.setCurrentTime(newTime)
  currentTime.value = newTime
}

// 切换时间系统运行状态
const toggleTimeSystem = () => {
  if (isTimeSystemRunning.value) {
    props.stopTimeSystem()
  } else {
    props.startTimeSystem()
  }
  isTimeSystemRunning.value = !isTimeSystemRunning.value
}

// 重置到当前时间
const resetToCurrentTime = () => {
  const now = new Date()
  props.setCurrentTime(now)
  currentTime.value = now
}

// 定时更新当前时间显示
let updateInterval: NodeJS.Timeout | null = null

onMounted(() => {
  // 🚀 初始化状态：重置到当前时间，设置1倍速
  console.log('🎮 时间控制面板初始化...')

  // 1. 重置到当前真实时间
  const now = new Date()
  props.setCurrentTime(now)
  currentTime.value = now

  // 2. 设置1倍速
  props.setTimeScale(1)
  timeScale.value = 1

  // 3. 启动时间系统
  props.startTimeSystem()
  isTimeSystemRunning.value = true

  console.log(`🎮 时间控制面板初始化完成 - 时间: ${now.toLocaleTimeString()}, 倍速: 1x`)

  // 每秒更新时间显示
  updateInterval = setInterval(() => {
    currentTime.value = props.getCurrentTime()
    timeScale.value = props.getTimeScale()
  }, 1000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.time-control-panel {
  position:fixed;
  left: 1vw !important;
  top: 1vw  !important;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 12px;
  max-width: 200px;
  transform: scale(0.4);
  transform-origin: 0 0;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.toggle-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-display {
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.current-time, .sun-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.current-time:last-child, .sun-info:last-child {
  margin-bottom: 0;
}

.label {
  color: rgba(255, 255, 255, 0.7);
}

.time, .value {
  color: #4CAF50;
  font-weight: 500;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.control-group label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  font-weight: 500;
}

.time-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.time-input:focus {
  outline: none;
  border-color: #4CAF50;
}

.speed-controls, .quick-time-controls {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.speed-btn, .preset-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s;
  flex: 1;
  min-width: 40px;
}

.speed-btn:hover, .preset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.speed-btn.active {
  background: #4CAF50;
  border-color: #4CAF50;
}

.play-controls {
  display: flex;
  gap: 8px;
}

.play-btn, .reset-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
  flex: 1;
}

.play-btn:hover, .reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.play-btn.active {
  background: #4CAF50;
  border-color: #4CAF50;
}
</style>