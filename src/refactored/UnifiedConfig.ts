/**
 * 统一配置管理
 * 整合所有系统配置参数，避免重复定义和未使用的配置
 */

// 🎯 LOD系统配置
export const LOD_CONFIG = {
  // 核心参数 - 正在使用
  DEFAULT_SWITCH_DISTANCE: 100,    // 默认切换距离（米）
  DEFAULT_FADE_DISTANCE: 10,       // 默认渐变距离（米）
  UPDATE_INTERVAL: 100,            // 更新间隔（毫秒）
  ENABLED: true,                   // 是否启用LOD
  
  // 性能参数
  MAX_LOADING_MODELS: 5,           // 最大同时加载模型数
  PRELOAD_DISTANCE: 200,           // 预加载距离（米）
  
  // 验证函数
  validate: (switchDistance: number, fadeDistance: number): boolean => {
    return switchDistance > 0 && 
           fadeDistance > 0 && 
           switchDistance > fadeDistance
  }
} as const

// 🗺️ 场景配置
export const SCENE_CONFIG = {
  // 地图中心坐标
  MAP_CENTER: [
    114.35962345673624 + 0.00011846300000684096 + 0.00002328700000475692 - 0.000011521000004677262, 
    30.564406727759334 + 0.00003711700000152973 + 0.00001035199999677161 + 0.0000011999999998124622
  ] as [number, number],

  // 模型配置
  MODEL_ALTITUDE: 10,              // 模型海拔高度
  MODEL_ROTATE: [0, 0, ((27.5 + 0.87) * Math.PI) / 180] as [number, number, number], // 模型旋转角度（弧度）
  MODEL_ROTATE_DEGREES: [0, 0, 28] as [number, number, number], // 模型旋转角度（度数）
  BASE_ALTITUDE: 0,                // 基准海拔
  DEFAULT_SCALE: 1,                // 默认缩放比例

  // 地图配置
  DEFAULT_ZOOM: 18,                // 地图默认缩放级别
  MIN_ZOOM: 10,                    // 最小缩放级别
  MAX_ZOOM: 22,                    // 最大缩放级别
  MIN_PITCH: 0,                    // 最小俯仰角
  MAX_PITCH: 60,                   // 最大俯仰角
} as const

// 🏗️ 模型配置
export const MODEL_CONFIG = {
  // 加载配置
  LOADING_TIMEOUT: 30000,          // 加载超时时间（毫秒）
  RETRY_COUNT: 3,                  // 重试次数
  
  // 缓存配置
  CACHE_SIZE: 100,                 // 缓存模型数量
  CACHE_DURATION: 300000,          // 缓存持续时间（毫秒）
  
  // 性能配置
  MAX_TRIANGLES: 1000000,          // 最大三角形数
  OPTIMIZATION_THRESHOLD: 500000,   // 优化阈值
} as const

// 🎮 交互配置
export const INTERACTION_CONFIG = {
  // 点击配置
  CLICK_TOLERANCE: 5,              // 点击容差（像素）
  DOUBLE_CLICK_DELAY: 300,         // 双击延迟（毫秒）
  
  // 动画配置
  ANIMATION_DURATION: 300,         // 动画持续时间（毫秒）
  EASING_FUNCTION: 'ease-out',     // 缓动函数
  
  // 楼层切换
  FLOOR_SWITCH_DELAY: 100,         // 楼层切换延迟（毫秒）
} as const

// 🔧 信标定位配置
export const BEACON_CONFIG = {
  TX_POWER: -59,                   // 发射功率
  PATH_LOSS_EXPONENT: 2.5,         // 路径损耗指数
  SMOOTHING_FACTOR: 0.2,           // 平滑因子
  MIN_BEACONS: 3,                  // 最小信标数量
  MAP_SCALE: 4.6,                  // 地图比例
} as const

// 🎨 UI配置
export const UI_CONFIG = {
  // 面板配置
  PANEL_WIDTH: 320,                // 面板宽度（像素）
  PANEL_MAX_HEIGHT: 'calc(100vh - 40px)', // 面板最大高度
  PANEL_Z_INDEX: 1000,             // 面板层级
  
  // 动画配置
  TRANSITION_DURATION: '0.3s',     // 过渡动画时长
  BACKDROP_BLUR: '10px',           // 背景模糊
  
  // 响应式断点
  MOBILE_BREAKPOINT: 768,          // 移动端断点（像素）
} as const

// 🔍 调试配置
export const DEBUG_CONFIG = {
  ENABLED: process.env.NODE_ENV === 'development', // 是否启用调试
  LOG_LEVEL: 'info',               // 日志级别
  PERFORMANCE_MONITORING: true,     // 性能监控
  SHOW_BOUNDING_BOXES: false,      // 显示包围盒
  SHOW_LOD_INFO: false,            // 显示LOD信息
} as const

// 📊 性能配置
export const PERFORMANCE_CONFIG = {
  // FPS配置
  TARGET_FPS: 60,                  // 目标帧率
  MIN_FPS: 30,                     // 最小帧率
  
  // 内存配置
  MAX_MEMORY_USAGE: 512 * 1024 * 1024, // 最大内存使用（字节）
  MEMORY_WARNING_THRESHOLD: 0.8,   // 内存警告阈值
  
  // 更新频率
  STATS_UPDATE_INTERVAL: 1000,     // 统计更新间隔（毫秒）
} as const

// 🔧 便捷访问函数
export const ConfigUtils = {
  /**
   * 获取LOD配置
   */
  getLODConfig() {
    return {
      switchDistance: LOD_CONFIG.DEFAULT_SWITCH_DISTANCE,
      fadeDistance: LOD_CONFIG.DEFAULT_FADE_DISTANCE,
      updateInterval: LOD_CONFIG.UPDATE_INTERVAL,
      enabled: LOD_CONFIG.ENABLED
    }
  },

  /**
   * 获取场景配置
   */
  getSceneConfig() {
    return {
      mapCenter: [...SCENE_CONFIG.MAP_CENTER] as [number, number],
      modelAltitude: SCENE_CONFIG.MODEL_ALTITUDE,
      modelRotate: [...SCENE_CONFIG.MODEL_ROTATE] as [number, number, number],
      baseAltitude: SCENE_CONFIG.BASE_ALTITUDE,
      defaultScale: SCENE_CONFIG.DEFAULT_SCALE,
      defaultZoom: SCENE_CONFIG.DEFAULT_ZOOM
    }
  },

  /**
   * 验证LOD配置
   */
  validateLODConfig(switchDistance: number, fadeDistance: number): boolean {
    return LOD_CONFIG.validate(switchDistance, fadeDistance)
  },

  /**
   * 获取调试信息
   */
  getDebugInfo(): string {
    return `
配置信息:
- LOD切换距离: ${LOD_CONFIG.DEFAULT_SWITCH_DISTANCE}m
- LOD渐变距离: ${LOD_CONFIG.DEFAULT_FADE_DISTANCE}m
- 地图中心: [${SCENE_CONFIG.MAP_CENTER[0].toFixed(6)}, ${SCENE_CONFIG.MAP_CENTER[1].toFixed(6)}]
- 默认缩放: ${SCENE_CONFIG.DEFAULT_ZOOM}
- 调试模式: ${DEBUG_CONFIG.ENABLED ? '启用' : '禁用'}
- 性能监控: ${DEBUG_CONFIG.PERFORMANCE_MONITORING ? '启用' : '禁用'}
    `.trim()
  }
}

// 🎯 导出统一配置对象
export const UNIFIED_CONFIG = {
  LOD: LOD_CONFIG,
  SCENE: SCENE_CONFIG,
  MODEL: MODEL_CONFIG,
  INTERACTION: INTERACTION_CONFIG,
  BEACON: BEACON_CONFIG,
  UI: UI_CONFIG,
  DEBUG: DEBUG_CONFIG,
  PERFORMANCE: PERFORMANCE_CONFIG,
  Utils: ConfigUtils
} as const

// 🔧 类型定义
export type UnifiedConfigType = typeof UNIFIED_CONFIG

// 📋 配置验证
export function validateAllConfigs(): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // 验证LOD配置
  if (!LOD_CONFIG.validate(LOD_CONFIG.DEFAULT_SWITCH_DISTANCE, LOD_CONFIG.DEFAULT_FADE_DISTANCE)) {
    errors.push('LOD配置无效：切换距离必须大于渐变距离')
  }

  // 验证场景配置
  if (SCENE_CONFIG.DEFAULT_ZOOM < SCENE_CONFIG.MIN_ZOOM || SCENE_CONFIG.DEFAULT_ZOOM > SCENE_CONFIG.MAX_ZOOM) {
    errors.push('场景配置无效：默认缩放级别超出范围')
  }

  // 验证性能配置
  if (PERFORMANCE_CONFIG.MIN_FPS > PERFORMANCE_CONFIG.TARGET_FPS) {
    errors.push('性能配置无效：最小帧率不能大于目标帧率')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// 🎯 默认导出
export default UNIFIED_CONFIG
