import * as THREE from "three"
// import mapboxgl from "mapbox-gl"
import * as SunCalc from "suncalc"

/**
 * 时间系统配置接口
 */
export interface TimeSystemConfig {
	/** 是否启用时间系统 */
	enabled?: boolean
	/** 纬度 */
	latitude: number
	/** 经度 */
	longitude: number
	/** 时间加速倍数，1为实时，24为一天24秒 */
	timeScale?: number
	/** 是否启用自动更新 */
	autoUpdate?: boolean
	/** 更新间隔（毫秒） */
	updateInterval?: number
}

/**
 * 太阳位置信息
 */
export interface SunPosition {
	/** 太阳高度角（弧度） */
	elevation: number
	/** 太阳方位角（弧度） */
	azimuth: number
	/** 太阳在Three.js坐标系中的方向向量 */
	direction: THREE.Vector3
	/** 是否为白天 */
	isDaytime: boolean
	/** 太阳强度 (0-1) */
	intensity: number
	/** 方向光强度 */
	directionalIntensity: number
	/** 环境光强度 */
	ambientIntensity: number
}

/**
 * 天空颜色配置
 */
export interface SkyColors {
	/** 地平线颜色 */
	horizonColor: string
	/** 天顶颜色 */
	zenithColor: string
	/** 雾气颜色 */
	fogColor?: string
	/** 星星强度 */
	starIntensity?: number
	/** 太阳是否可见 */
	sunVisible?: boolean
	/** 月亮是否可见 */
	moonVisible?: boolean
	/** 地平线混合强度 */
	horizonBlend?: number
	/** 雾气范围 */
	fogRange?: [number, number]
}

/**
 * 全局时间系统
 * 管理实时时间、太阳位置计算、天空颜色变化和地图亮度
 */
export class TimeSystem {
	private config: Required<TimeSystemConfig>
	private currentTime: Date
	private animationId: number | null = null
	private lastUpdateTime: number = 0

	// 🔄 双循环架构优化：分离高频和低频更新
	private _lastCallbackTime?: number
	private _lastEnvironmentUpdate?: number
	private _lastSunPosition?: SunPosition

	// 🌞 全局光照状态缓存（用于快速循环）
	private cachedLightingState: {
		sunPosition: SunPosition
		skyColors: SkyColors
		mapBrightness: number
		lastCalculated: number
	} | null = null

	private callbacks: {
		// 高频回调：时间显示等UI更新（每秒）
		onTimeUpdate?: (time: Date, sunPosition: SunPosition) => void
		// 低频回调：环境效果更新（每分钟或显著变化时）
		onSkyColorChange?: (colors: SkyColors) => void
		onMapBrightnessChange?: (brightness: number) => void
	} = {}

	constructor(config: TimeSystemConfig) {
		this.config = {
			enabled: config.enabled ?? true,
			latitude: config.latitude,
			longitude: config.longitude,
			timeScale: config.timeScale || 1, // 🎯 默认1倍速
			autoUpdate: config.autoUpdate !== false,
			updateInterval: config.updateInterval || 100 // 100ms更新间隔
		}
		this.currentTime = new Date() // 🕐 默认使用当前时间

		// 🎨 立即计算初始光照状态
		this.initializeInitialLighting()
	}

	/**
	 * 🎨 初始化初始光照状态
	 */
	private initializeInitialLighting(): void {
		try {
			const sunPosition = this.calculateSunPosition(this.currentTime)
			const skyColors = this.calculateSkyColors(sunPosition)
			const mapBrightness = this.calculateMapBrightness(sunPosition)

			// 缓存初始光照状态
			this.cachedLightingState = {
				sunPosition,
				skyColors,
				mapBrightness,
				lastCalculated: performance.now()
			}

			console.log(`🎨 初始光照状态已计算 - 时间: ${this.currentTime.toLocaleTimeString()}`)
		} catch (error) {
			console.warn("初始化光照状态失败:", error)
		}
	}

	/**
	 * 🚀 应用初始光照状态
	 * 在start()时调用，确保初始光照正确应用
	 */
	public applyInitialLighting(): void {
		try {
			if (this.cachedLightingState) {
				console.log('🚀 应用初始光照状态...')

				// 触发光照更新回调
				this.callbacks.onTimeUpdate?.(this.currentTime, this.cachedLightingState.sunPosition)
				this.callbacks.onSkyColorChange?.(this.cachedLightingState.skyColors)
				this.callbacks.onMapBrightnessChange?.(this.cachedLightingState.mapBrightness)

				console.log('✅ 初始光照状态已应用')
			} else {
				console.warn('⚠️ 初始光照状态未计算，重新计算...')
				this.initializeInitialLighting()
				// 递归调用确保应用
				setTimeout(() => this.applyInitialLighting(), 100)
			}
		} catch (error) {
			console.warn("应用初始光照状态失败:", error)
		}
	}

	/**
	 * 启动时间系统
	 */
	public start(): void {
		if (this.config.autoUpdate && !this.animationId) {
			this.update()
		}
	}

	/**
	 * 停止时间系统
	 */
	public stop(): void {
		if (this.animationId) {
			cancelAnimationFrame(this.animationId)
			this.animationId = null
		}
	}

	/**
	 * 🔄 优化的双循环更新架构
	 * 主更新循环：处理时间推进和高频UI更新
	 */
	private update = (): void => {
		const now = performance.now()
		if (now - this.lastUpdateTime >= this.config.updateInterval) {
			// 1. 更新时间
			const deltaTime = (now - this.lastUpdateTime) * this.config.timeScale
			this.currentTime = new Date(this.currentTime.getTime() + deltaTime)

			// 2. 高频更新：时间显示（每秒更新）
			if (!this._lastCallbackTime || now - this._lastCallbackTime > 1000) {
				this._lastCallbackTime = now

				// 从缓存获取太阳位置，避免重复计算
				const sunPosition = this.getCachedSunPosition()
				this.callbacks.onTimeUpdate?.(this.currentTime, sunPosition)
			}

			// 3. 慢速循环检查：环境效果（每分钟更新，或显著变化时）
			this.checkAndUpdateEnvironment(now)

			this.lastUpdateTime = now
		}

		if (this.config.autoUpdate) {
			this.animationId = requestAnimationFrame(this.update)
		}
	}

	/**
	 * 🐌 慢速循环：环境效果更新
	 * 只在必要时进行昂贵的光照计算
	 */
	private checkAndUpdateEnvironment(now: number): void {
		const shouldUpdateEnvironment =
			!this._lastEnvironmentUpdate ||
			now - this._lastEnvironmentUpdate > 60000 || // 60秒强制更新
			this.needsEnvironmentUpdate()

		if (shouldUpdateEnvironment) {
			console.log('🐌 慢速循环：更新环境光照状态')
			this._lastEnvironmentUpdate = now

			// 重新计算太阳位置和环境状态
			const sunPosition = this.calculateSunPosition(this.currentTime)
			const skyColors = this.calculateSkyColors(sunPosition)
			const mapBrightness = this.calculateMapBrightness(sunPosition)

			// 更新缓存状态
			this.cachedLightingState = {
				sunPosition,
				skyColors,
				mapBrightness,
				lastCalculated: now
			}

			this._lastSunPosition = sunPosition

			// 触发环境更新回调
			this.callbacks.onSkyColorChange?.(skyColors)
			this.callbacks.onMapBrightnessChange?.(mapBrightness)
		}
	}

	/**
	 * 计算太阳位置
	 * 使用suncalc.js专业天文库进行精确计算
	 */
	private calculateSunPosition(time: Date): SunPosition {
		// 使用suncalc.js计算太阳位置（天文级精度）
		const sunPosition = SunCalc.getPosition(time, this.config.latitude, this.config.longitude)
		const sunTimes = SunCalc.getTimes(time, this.config.latitude, this.config.longitude)

		// suncalc返回的角度已经是弧度
		const elevation = sunPosition.altitude  // 太阳高度角（弧度）
		const azimuth = sunPosition.azimuth     // 太阳方位角（弧度，从南方开始顺时针）

		// 转换为Three.js坐标系的方向向量
		// suncalc的方位角是从南方开始顺时针，需要转换为Three.js坐标系
		const direction = new THREE.Vector3(
			Math.sin(azimuth) * Math.cos(elevation),
			Math.sin(elevation),
			-Math.cos(azimuth) * Math.cos(elevation)
		).normalize()

		// 使用suncalc的日出日落时间判断白天/夜晚
		const isDaytime = time >= sunTimes.sunrise && time <= sunTimes.sunset

		// 计算分离的光照强度
		const lightingIntensity = this.calculateLightingIntensity(elevation)

		// 保持向后兼容的总强度
		const intensity = Math.max(lightingIntensity.directional, lightingIntensity.ambient)

		console.log(`🌞 SunCalc计算结果 - 时间: ${time.toLocaleTimeString()}, 高度角: ${(elevation * 180/Math.PI).toFixed(1)}°, 方位角: ${(azimuth * 180/Math.PI).toFixed(1)}°, 白天: ${isDaytime}`)

		return {
			elevation,
			azimuth,
			direction,
			isDaytime,
			intensity,
			directionalIntensity: lightingIntensity.directional,
			ambientIntensity: lightingIntensity.ambient
		}
	}

	/**
	 * 计算分离的光照强度
	 * @param elevation 太阳高度角（弧度）
	 * @returns 方向光和环境光的强度
	 */
	private calculateLightingIntensity(elevation: number): { directional: number; ambient: number } {
		const elevationDegrees = elevation * (180 / Math.PI)
		
		if (elevationDegrees < -18) {
			// 深夜：只有微弱的月光和星光
			return { directional: 0.01, ambient: 0.05 }
		} else if (elevationDegrees < -6) {
			// 天文暮光/晨光：平滑过渡
			const factor = Math.pow((elevationDegrees + 18) / 12, 0.8) // 使用幂函数实现更平滑的过渡
			return { 
				directional: 0.01 + factor * 0.15, 
				ambient: 0.05 + factor * 0.20 
			}
		} else if (elevationDegrees < 0) {
			// 民用暮光/晨光：继续平滑过渡
			const factor = Math.pow((elevationDegrees + 6) / 6, 0.7)
			return { 
				directional: 0.16 + factor * 0.24, 
				ambient: 0.25 + factor * 0.15 
			}
		} else if (elevationDegrees < 15) {
			// 日出/日落时段：温和的强度增长
			const factor = Math.pow(elevationDegrees / 15, 0.6)
			return { 
				directional: 0.40 + factor * 0.45, 
				ambient: 0.40 + factor * 0.15 
			}
		} else {
			// 白天：根据太阳高度平滑调整
			const sinElevation = Math.sin(elevation)
			const maxIntensity = Math.min(0.95, sinElevation * 1.1) // 避免过度曝光
			return { 
				directional: Math.max(0.85, maxIntensity), 
				ambient: Math.min(0.55, 0.35 + sinElevation * 0.2) // 环境光保持相对稳定
			}
		}
	}

	/**
	 * 计算天空颜色
	 */
	private calculateSkyColors(sunPosition: SunPosition): SkyColors {
		const { elevation, azimuth } = sunPosition
		
		// 根据太阳高度角计算天空颜色，增加更自然的过渡
		if (elevation > 0) {
			// 白天 - 根据太阳高度调整颜色
			const dayIntensity = Math.min(1, elevation / (Math.PI / 2))
			const sunsetFactor = elevation < 0.26 ? (0.26 - elevation) / 0.26 : 0 // 日出日落效果（约15度）
			
			if (sunsetFactor > 0) {
				// 日出日落时的橙红色天空
				const orangeR = Math.floor(255 - sunsetFactor * 50)
				const orangeG = Math.floor(140 + sunsetFactor * 60)
				const orangeB = Math.floor(50 + sunsetFactor * 100)
				
				const purpleR = Math.floor(100 + sunsetFactor * 80)
				const purpleG = Math.floor(50 + sunsetFactor * 100)
				const purpleB = Math.floor(150 + sunsetFactor * 80)
				
				return {
					horizonColor: `rgba(${orangeR}, ${orangeG}, ${orangeB}, 1)`,
					zenithColor: `rgba(${purpleR}, ${purpleG}, ${purpleB}, 1)`,
					fogColor: `rgba(${orangeR}, ${orangeG}, ${orangeB}, 0.8)`,
					starIntensity: Math.max(0, sunsetFactor * 0.3),
					sunVisible: true,
					moonVisible: false,
					horizonBlend: 0.15,
					fogRange: [0.5, 15]
				}
			} else {
				// 正常白天的蓝色天空
				const horizonR = Math.floor(135 + (255 - 135) * dayIntensity)
				const horizonG = Math.floor(206 + (255 - 206) * dayIntensity)
				const horizonB = Math.floor(250)
				
				const zenithR = Math.floor(25 + (100 - 25) * dayIntensity)
				const zenithG = Math.floor(25 + (149 - 25) * dayIntensity)
				const zenithB = Math.floor(112 + (237 - 112) * dayIntensity)
				
				return {
					horizonColor: `rgba(${horizonR}, ${horizonG}, ${horizonB}, 1)`,
					zenithColor: `rgba(${zenithR}, ${zenithG}, ${zenithB}, 1)`,
					fogColor: `rgba(${horizonR}, ${horizonG}, ${horizonB}, 0.8)`,
					starIntensity: 0,
					sunVisible: true,
					moonVisible: false,
					horizonBlend: 0.05,
					fogRange: [1, 20]
				}
			}
		} else {
			// 夜晚 - 深蓝到黑色的渐变
			const nightIntensity = Math.min(1, Math.abs(elevation) / (Math.PI / 6))
			const twilightFactor = Math.abs(elevation) < 0.31 ? (0.31 - Math.abs(elevation)) / 0.31 : 0 // 黄昏效果（约18度）
			
			if (twilightFactor > 0) {
				// 黄昏时的深紫色天空
				const purpleR = Math.floor(40 + twilightFactor * 60)
				const purpleG = Math.floor(20 + twilightFactor * 40)
				const purpleB = Math.floor(80 + twilightFactor * 100)
				
				const darkPurpleR = Math.floor(20 + twilightFactor * 40)
				const darkPurpleG = Math.floor(10 + twilightFactor * 30)
				const darkPurpleB = Math.floor(60 + twilightFactor * 80)
				
				return {
					horizonColor: `rgba(${purpleR}, ${purpleG}, ${purpleB}, 1)`,
					zenithColor: `rgba(${darkPurpleR}, ${darkPurpleG}, ${darkPurpleB}, 1)`,
					fogColor: `rgba(${purpleR}, ${purpleG}, ${purpleB}, 0.9)`,
					starIntensity: Math.max(0.3, 1 - twilightFactor * 0.7),
					sunVisible: false,
					moonVisible: true,
					horizonBlend: 0.2,
					fogRange: [0.3, 12]
				}
			} else {
				// 深夜的深蓝黑色天空
				const baseR = Math.floor(5 + nightIntensity * 10)
				const baseG = Math.floor(8 + nightIntensity * 15)
				const baseB = Math.floor(25 + nightIntensity * 35)
				
				return {
					horizonColor: `rgba(${baseR}, ${baseG}, ${baseB}, 1)`,
					zenithColor: `rgba(${Math.floor(baseR * 0.3)}, ${Math.floor(baseG * 0.3)}, ${Math.floor(baseB * 0.6)}, 1)`,
					fogColor: `rgba(${baseR}, ${baseG}, ${baseB}, 0.9)`,
					starIntensity: Math.min(0.9, 0.5 + nightIntensity * 0.4),
					sunVisible: false,
					moonVisible: true,
					horizonBlend: 0.25,
					fogRange: [0.2, 8]
				}
			}
		}
	}

	/**
	 * 计算地图亮度
	 */
	private calculateMapBrightness(sunPosition: SunPosition): number {
		const { elevation, isDaytime } = sunPosition
		
		if (isDaytime) {
			// 白天亮度 0.8-1.0
			return 0.8 + 0.2 * Math.max(0, Math.sin(elevation))
		} else {
			// 夜晚亮度 0.3-0.6
			const nightFactor = Math.abs(elevation) / (Math.PI / 2)
			return 0.6 - 0.3 * nightFactor
		}
	}

	/**
	 * 🚀 快速循环：获取缓存的太阳位置
	 * 避免在高频更新中重复计算
	 */
	private getCachedSunPosition(): SunPosition {
		// 如果缓存存在且不超过5分钟，直接返回缓存
		if (this.cachedLightingState &&
			performance.now() - this.cachedLightingState.lastCalculated < 300000) {
			return this.cachedLightingState.sunPosition
		}

		// 否则重新计算并缓存
		const sunPosition = this.calculateSunPosition(this.currentTime)
		if (!this.cachedLightingState) {
			this.cachedLightingState = {
				sunPosition,
				skyColors: this.calculateSkyColors(sunPosition),
				mapBrightness: this.calculateMapBrightness(sunPosition),
				lastCalculated: performance.now()
			}
		} else {
			this.cachedLightingState.sunPosition = sunPosition
			this.cachedLightingState.lastCalculated = performance.now()
		}

		return sunPosition
	}

	/**
	 * 🔍 检查是否需要环境更新
	 * 基于时间变化和太阳位置变化的智能判断
	 */
	private needsEnvironmentUpdate(): boolean {
		if (!this._lastSunPosition || !this.cachedLightingState) {
			return true
		}

		// 计算当前太阳位置（轻量级检查）
		const currentSunPosition = this.calculateSunPosition(this.currentTime)

		const elevationDiff = Math.abs(currentSunPosition.elevation - this._lastSunPosition.elevation)
		const azimuthDiff = Math.abs(currentSunPosition.azimuth - this._lastSunPosition.azimuth)
		const intensityDiff = Math.abs(currentSunPosition.intensity - this._lastSunPosition.intensity)

		// 优化的阈值：更敏感的变化检测
		return elevationDiff > (3 * Math.PI / 180) ||  // 3度高度角变化
			   azimuthDiff > (8 * Math.PI / 180) ||    // 8度方位角变化
			   intensityDiff > 0.08 ||                 // 8%强度变化
			   currentSunPosition.isDaytime !== this._lastSunPosition.isDaytime // 昼夜切换
	}

	/**
	 * 🎯 获取当前光照状态（用于外部快速访问）
	 * 这是为Mapbox渲染循环提供的快速接口
	 */
	public getCurrentLightingState(): {
		sunPosition: SunPosition
		skyColors: SkyColors
		mapBrightness: number
	} | null {
		return this.cachedLightingState
	}

	/**
	 * 设置时间更新回调
	 */
	public onTimeUpdate(callback: (time: Date, sunPosition: SunPosition) => void): void {
		this.callbacks.onTimeUpdate = callback
	}

	/**
	 * 设置天空颜色变化回调
	 */
	public onSkyColorChange(callback: (colors: SkyColors) => void): void {
		this.callbacks.onSkyColorChange = callback
	}

	/**
	 * 设置地图亮度变化回调
	 */
	public onMapBrightnessChange(callback: (brightness: number) => void): void {
		this.callbacks.onMapBrightnessChange = callback
	}

	/**
	 * 获取当前时间
	 */
	public getCurrentTime(): Date {
		return new Date(this.currentTime)
	}

	/**
	 * 设置当前时间
	 */
	public setCurrentTime(time: Date): void {
		this.currentTime = new Date(time)
	}

	/**
	 * 获取当前太阳位置
	 */
	public getCurrentSunPosition(): SunPosition {
		return this.calculateSunPosition(this.currentTime)
	}

	/**
	 * 设置时间加速倍数
	 */
	public setTimeScale(scale: number): void {
		this.config.timeScale = scale
	}

	/**
	 * 获取时间加速倍数
	 */
	public getTimeScale(): number {
		return this.config.timeScale
	}

	/**
	 * 销毁时间系统
	 */
	public dispose(): void {
		this.stop()
		this.callbacks = {}
	}
}