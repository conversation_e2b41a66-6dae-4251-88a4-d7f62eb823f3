import * as THREE from "three"

/**
 * 模型节点类型枚举
 */
export enum ModelNodeType {
	/** 根场景节点 */
	SCENE = "scene",
	/** 建筑外立面 */
	BUILDING_EXTERIOR = "building_exterior",
	/** 建筑室内楼层 */
	BUILDING_FLOOR = "building_floor",
	/** 普通模型 */
	MODEL = "model",
	/** 模型组 */
	GROUP = "group",
}

/**
 * 模型加载状态
 */
export enum ModelLoadState {
	/** 未开始加载 */
	PENDING = "pending",
	/** 加载中 */
	LOADING = "loading",
	/** 加载完成 */
	LOADED = "loaded",
	/** 加载失败 */
	ERROR = "error",
}

/**
 * 模型资源配置
 */
export interface ModelResource {
	/** 模型文件类型 */
	type?: "obj" | "fbx" | "gltf" | "glb"
	/** OBJ文件路径 */
	objPath?: string
	/** MTL文件路径 */
	mtlPath?: string
	/** FBX文件路径 */
	fbxPath?: string
	/** GLTF/GLB文件路径 */
	gltfPath?: string
	/** 纹理文件路径（可选） */
	texturePaths?: string[]
	/** 模型缩放比例（可选） */
	scale?: number
	/** 模型旋转（可选） */
	rotation?: { x?: number; y?: number; z?: number }
}

/**
 * 变换配置
 */
export interface Transform {
	/** 位置 */
	position?: { x: number; y: number; z: number }
	/** 旋转（弧度） */
	rotation?: { x: number; y: number; z: number }
	/** 缩放 */
	scale?: { x: number; y: number; z: number }
}

/**
 * 基础模型节点数据
 */
export interface BaseModelNode {
	/** 唯一标识符 */
	id: string
	/** 显示名称 */
	name: string
	/** 节点类型 */
	type: ModelNodeType
	/** 父节点ID，根节点为null */
	parentId: string | null
	/** 加载状态 */
	loadState: ModelLoadState
	/** 是否可见 */
	visible: boolean
	/** 变换配置 */
	transform?: Transform
	/** 创建时间 */
	createdAt: Date
	/** 更新时间 */
	updatedAt: Date
}

/**
 * 场景根节点
 */
export interface SceneNode extends BaseModelNode {
	type: ModelNodeType.SCENE
	/** 场景描述 */
	description?: string
}

/**
 * 建筑外立面节点
 */
export interface BuildingExteriorNode extends BaseModelNode {
	type: ModelNodeType.BUILDING_EXTERIOR
	/** 模型资源 */
	resource: ModelResource
	/** 建筑中心点坐标 */
	centerPosition: { x: number; y: number; z: number }
	/** LOD配置（如果有室内楼层） */
	lodConfig?: LODConfig
	/** 是否有室内楼层 */
	hasInterior: boolean
}

/**
 * 建筑室内楼层节点
 */
export interface BuildingFloorNode extends BaseModelNode {
	type: ModelNodeType.BUILDING_FLOOR
	/** 模型资源 */
	resource: ModelResource
	/** 楼层号 */
	floor: number
	/** 楼层名称 */
	floorName: string
	/** 楼层高度 */
	height?: number
	/** 是否为默认显示楼层 */
	isDefault: boolean
}

/**
 * 普通模型节点
 */
export interface ModelNode extends BaseModelNode {
	type: ModelNodeType.MODEL
	/** 模型资源 */
	resource: ModelResource
}

/**
 * 模型组节点
 */
export interface GroupNode extends BaseModelNode {
	type: ModelNodeType.GROUP
	/** 组描述 */
	description?: string
}

/**
 * 联合类型：所有模型节点类型
 */
export type ModelNodeData =
	| SceneNode
	| BuildingExteriorNode
	| BuildingFloorNode
	| ModelNode
	| GroupNode

/**
 * 模型树管理器接口
 */
export interface ModelTreeManager {
	/** 获取所有节点 */
	getAllNodes(): ModelNodeData[]

	/** 根据ID获取节点 */
	getNodeById(id: string): ModelNodeData | undefined

	/** 获取子节点 */
	getChildren(parentId: string): ModelNodeData[]

	/** 获取根节点 */
	getRootNode(): SceneNode

	/** 添加节点 */
	addNode(node: ModelNodeData): void

	/** 更新节点 */
	updateNode(id: string, updates: Partial<ModelNodeData>): void

	/** 删除节点 */
	removeNode(id: string): void

	/** 获取建筑的所有楼层 */
	getBuildingFloors(buildingId: string): BuildingFloorNode[]

	/** 获取需要LOD的建筑 */
	getLODBuildings(): BuildingExteriorNode[]
}

/**
 * 运行时模型数据（包含Three.js对象）
 */
export interface RuntimeModelData {
	/** 节点数据 */
	nodeData: ModelNodeData
	/** Three.js对象 */
	object3D?: THREE.Object3D
	/** 加载进度 */
	loadProgress: number
	/** 错误信息 */
	error?: string
}

/**
 * 预设的场景根节点
 */
export const SCENE_ROOT_NODE: SceneNode = {
	id: "scene_root",
	name: "场景根节点",
	type: ModelNodeType.SCENE,
	parentId: null,
	loadState: ModelLoadState.LOADED,
	visible: true,
	description: "湖北省博物馆3D场景",
	createdAt: new Date(),
	updatedAt: new Date(),
}

/**
 * 创建建筑外立面节点的工厂函数
 */
export function createBuildingExteriorNode(
	id: string,
	name: string,
	resource: ModelResource,
	centerPosition: { x: number; y: number; z: number },
	hasInterior: boolean = false,
	lodConfig?: LODConfig
): BuildingExteriorNode {
	return {
		id,
		name,
		type: ModelNodeType.BUILDING_EXTERIOR,
		parentId: SCENE_ROOT_NODE.id,
		loadState: ModelLoadState.PENDING,
		visible: true,
		resource,
		centerPosition,
		hasInterior,
		lodConfig,
		createdAt: new Date(),
		updatedAt: new Date(),
	}
}

/**
 * 创建建筑楼层节点的工厂函数
 */
export function createBuildingFloorNode(
	id: string,
	name: string,
	buildingId: string,
	resource: ModelResource,
	floor: number,
	floorName: string,
	isDefault: boolean = false
): BuildingFloorNode {
	return {
		id,
		name,
		type: ModelNodeType.BUILDING_FLOOR,
		parentId: buildingId,
		loadState: ModelLoadState.PENDING,
		visible: isDefault,
		resource,
		floor,
		floorName,
		isDefault,
		createdAt: new Date(),
		updatedAt: new Date(),
	}
}

/**
 * 创建普通模型节点的工厂函数
 */
export function createModelNode(
	id: string,
	name: string,
	resource: ModelResource,
	parentId: string = SCENE_ROOT_NODE.id
): ModelNode {
	return {
		id,
		name,
		type: ModelNodeType.MODEL,
		parentId,
		loadState: ModelLoadState.PENDING,
		visible: true,
		resource,
		createdAt: new Date(),
		updatedAt: new Date(),
	}
}
