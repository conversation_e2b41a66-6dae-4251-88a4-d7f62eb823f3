//tianditu key
const tiandituKey = "14efe482d0dc5ab5e23601e0fa83cae0";

// 定义天地图源
const tdtVecSource = {
  type: "raster",
  tiles: [
    `http://t0.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t1.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t2.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t3.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t4.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t5.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t6.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t7.tianditu.com/vec_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
  ],
  tileSize: 256,
  maxzoom: 18,
};

const tdtCvaSource = {
  type: "raster",
  tiles: [
    `http://t0.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t1.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t2.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t3.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t4.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t5.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t6.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
    `http://t7.tianditu.com/cva_w/wmts?tk=${tiandituKey}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles`,
  ],
  tileSize: 256,
  maxzoom: 18,
};

export default {
  version: 8,
  sources: {
    // 添加矢量底图源
    "tdt-vec-source": tdtVecSource,
    // 添加矢量注记源
    "tdt-cva-source": tdtCvaSource,
  },
  layers: [
    // 添加矢量底图图层
    {
      id: "tdt-vec-layer",
      type: "raster",
      source: "tdt-vec-source",
    },
    // 添加矢量注记图层
    {
      id: "tdt-cva-layer",
      type: "raster",
      source: "tdt-cva-source",
    },
  ],
};
