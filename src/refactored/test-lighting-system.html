<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 光照系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-section.warning {
            border-left-color: #FF9800;
        }
        .test-section.error {
            border-left-color: #F44336;
        }
        .code-block {
            background: #1e1e1e;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #4CAF50;
            color: #000;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .star-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .star-intensity {
            font-size: 24px;
        }
        .time-period {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 光照系统重构测试</h1>
        
        <div class="test-section">
            <h2>✅ 解决方案概述</h2>
            <p>成功解决了 <code>updateSunPosition</code>、<code>updateSkyColors</code> 和 <code>updateMapboxSkyWithConfig</code> 之间的重复冲突问题。</p>
            
            <h3>🎯 核心改进</h3>
            <ul>
                <li><span class="highlight">统一配置驱动</span>：完全使用配置系统管理光照</li>
                <li><span class="highlight">动态星星强度</span>：确保星星在夜晚正确显示</li>
                <li><span class="highlight">FOG+SKY协作</span>：Mapbox FOG 和 SKY 组件完美配合</li>
                <li><span class="highlight">消除冲突</span>：移除旧的重复调用机制</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🌟 星星显示测试</h2>
            <p>系统现在能正确处理不同时段的星星显示：</p>
            
            <div class="time-period">
                <div class="star-demo">
                    <span>🌞 白天 (7:00-17:00):</span>
                    <span class="star-intensity">☀️</span>
                    <code>star-intensity: 0</code>
                </div>
            </div>
            
            <div class="time-period">
                <div class="star-demo">
                    <span>🌅 黄昏 (17:00-19:00):</span>
                    <span class="star-intensity">⭐</span>
                    <code>star-intensity: 0.3</code>
                </div>
            </div>
            
            <div class="time-period">
                <div class="star-demo">
                    <span>🌙 夜晚 (19:00-5:00):</span>
                    <span class="star-intensity">🌟✨⭐</span>
                    <code>star-intensity: 1.0</code>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 关键代码改进</h2>
            
            <h3>1. 统一光照更新入口</h3>
            <div class="code-block">
// 🌌 统一光照系统更新 - 完全使用配置驱动
this.timeSystem.onSkyColorChange((skyColors: SkyColors) => {
    const lightingState = this.timeSystem.getCurrentLightingState()
    if (lightingState && this.timeSystem) {
        // 🎨 使用新的统一光照配置系统
        const currentTime = this.timeSystem.getCurrentTime()
        const newLightingConfig = getLightingConfig(currentTime)
        
        // 🌟 关键：将 skyColors 中的星星强度合并到配置中
        const enhancedConfig = this.enhanceConfigWithSkyColors(newLightingConfig, skyColors)
        
        this.applyCompleteLightingConfig(enhancedConfig, lightingState.sunPosition)
        this.repaint(true)
    }
})
            </div>

            <h3>2. 动态配置增强</h3>
            <div class="code-block">
private enhanceConfigWithSkyColors(config: CompleteLightingConfig, skyColors: SkyColors) {
    const enhancedConfig = JSON.parse(JSON.stringify(config))
    
    // 🌟 关键：使用动态计算的星星强度覆盖预设值
    if (skyColors.starIntensity !== undefined) {
        enhancedConfig.sky.starIntensity = skyColors.starIntensity
    }
    
    return enhancedConfig
}
            </div>

            <h3>3. FOG 和 SKY 组件配合</h3>
            <div class="code-block">
// 🌫️ 更新FOG效果（关键：包含星星显示）
this.map.setFog({
    "color": skyConfig.fogColor,
    "horizon-blend": skyConfig.horizonBlend,
    "range": skyConfig.fogRange,
    "high-color": skyConfig.highColor,
    "space-color": skyConfig.spaceColor,
    "star-intensity": skyConfig.starIntensity  // 🌟 星星显示的关键
})
            </div>
        </div>

        <div class="test-section warning">
            <h2>🗑️ 已移除的旧方法</h2>
            <p>为避免冲突，以下方法已被移除：</p>
            <ul>
                <li><code>updateSunPosition()</code> - 现在使用统一配置系统</li>
                <li><code>updateMapboxSkyLayer()</code> - 现在通过 updateMapboxSkyWithConfig 处理</li>
                <li><code>updateSkyColors()</code> - FOG 和星星现在通过 updateMapboxSkyWithConfig 处理</li>
                <li><code>updateMapboxLighting()</code> - 地图亮度现在通过 updateMapboxMapStyle 处理</li>
                <li><code>updateMapBrightness()</code> - 地图亮度现在通过配置系统处理</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🚀 测试建议</h2>
            <ol>
                <li><strong>时间切换测试</strong>：在不同时段之间快速切换，观察星星显示是否正确</li>
                <li><strong>夜晚星空测试</strong>：将时间设置为夜晚（如 22:00），确认星星明亮显示</li>
                <li><strong>黄昏过渡测试</strong>：观察黄昏时段星星的渐现效果</li>
                <li><strong>FOG效果测试</strong>：检查不同时段的大气效果是否自然</li>
                <li><strong>性能测试</strong>：确认没有重复的 Mapbox API 调用</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>📊 预期效果</h2>
            <ul>
                <li>✅ 星星在夜晚正确显示</li>
                <li>✅ FOG 和 SKY 组件完美配合</li>
                <li>✅ 无重复冲突的光照更新</li>
                <li>✅ 统一的配置管理体验</li>
                <li>✅ 更好的性能表现</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🌟 光照系统重构测试页面已加载');
        console.log('📝 请在实际应用中测试以下功能：');
        console.log('1. 时间切换时的星星显示');
        console.log('2. 夜晚星空的明亮度');
        console.log('3. FOG 效果的自然过渡');
        console.log('4. 系统性能和稳定性');
    </script>
</body>
</html>
