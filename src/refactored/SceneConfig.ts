/**
 * 统一的场景配置参数
 * 所有坐标转换器、标注管理器等都应该使用这些统一的参数
 */

// 🎯 统一的场景参数配置
export const SCENE_CONFIG = {
  // 地图中心坐标（与MapApplication.ts完全一致）
  MAP_CENTER: [
    114.35962345673624 + 0.00011846300000684096 + 0.00002328700000475692 - 0.000011521000004677262, 
    30.564406727759334 + 0.00003711700000152973 + 0.00001035199999677161 + 0.0000011999999998124622
  ] as [number, number],

  // 模型海拔高度
  MODEL_ALTITUDE: 10,

  // 模型旋转角度（弧度）
  MODEL_ROTATE: [90 * Math.PI / 180, 0, ((27.5 + 0.87) * Math.PI) / 180] as [number, number, number],
// MODEL_ROTATE: [90 * Math.PI / 180, 0, ((151.6) * Math.PI) / 180] as [number, number, number],
  // 基准海拔（用于坐标转换）
  BASE_ALTITUDE: 0,

  // 默认缩放比例
  DEFAULT_SCALE: 1,

  // 地图默认缩放级别
  DEFAULT_ZOOM: 18,

  // 地图缩放范围
  MIN_ZOOM: 10,
  MAX_ZOOM: 22,

  // 地图俯仰角范围
  MIN_PITCH: 0,
  MAX_PITCH: 60,
} as const

// 🔧 便捷的访问函数
export const SceneConfig = {
  /**
   * 获取地图中心坐标
   */
  getMapCenter(): [number, number] {
    return [...SCENE_CONFIG.MAP_CENTER]
  },

  /**
   * 获取模型旋转角度（弧度）
   */
  getModelRotation(): [number, number, number] {
    return [...SCENE_CONFIG.MODEL_ROTATE]
  },

  /**
   * 获取模型旋转角度（度数）
   */
  // getModelRotationDegrees(): [number, number, number] {
  //   return [...SCENE_CONFIG.MODEL_ROTATE_DEGREES]
  // },

  /**
   * 获取模型海拔高度
   */
  getModelAltitude(): number {
    return SCENE_CONFIG.MODEL_ALTITUDE
  },

  /**
   * 获取基准海拔
   */
  getBaseAltitude(): number {
    return SCENE_CONFIG.BASE_ALTITUDE
  },

  /**
   * 获取默认缩放比例
   */
  getDefaultScale(): number {
    return SCENE_CONFIG.DEFAULT_SCALE
  },

  /**
   * 获取地图默认缩放级别
   */
  getDefaultZoom(): number {
    return SCENE_CONFIG.DEFAULT_ZOOM
  },

  /**
   * 获取完整的配置对象（只读）
   */
  getConfig(): typeof SCENE_CONFIG {
    return SCENE_CONFIG
  },

  /**
   * 验证坐标是否在合理范围内
   */
  validateCoordinate(lng: number, lat: number): boolean {
    return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90
  },

  /**
   * 计算与地图中心的距离（米）
   */
  distanceFromCenter(lng: number, lat: number): number {
    const [centerLng, centerLat] = SCENE_CONFIG.MAP_CENTER
    const R = 6378137 // 地球半径（米）
    const dLat = (lat - centerLat) * Math.PI / 180
    const dLng = (lng - centerLng) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
             Math.cos(centerLat * Math.PI / 180) * Math.cos(lat * Math.PI / 180) *
             Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  },

  /**
   * 检查坐标是否在合理的工作范围内（距离中心不超过指定距离）
   */
  isWithinWorkingRange(lng: number, lat: number, maxDistanceMeters: number = 10000): boolean {
    return this.distanceFromCenter(lng, lat) <= maxDistanceMeters
  }
}

// 🎯 类型定义
export interface SceneParameters {
  mapCenter: [number, number]
  modelAltitude: number
  modelRotate: [number, number, number]
  baseAltitude: number
  scale?: number
}

// 🔧 创建标准场景参数的工厂函数
export function createSceneParameters(overrides?: Partial<SceneParameters>): SceneParameters {
  return {
    mapCenter: overrides?.mapCenter || SceneConfig.getMapCenter(),
    modelAltitude: overrides?.modelAltitude || SceneConfig.getModelAltitude(),
    modelRotate: overrides?.modelRotate || SceneConfig.getModelRotation(),
    baseAltitude: overrides?.baseAltitude || SceneConfig.getBaseAltitude(),
    scale: overrides?.scale || SceneConfig.getDefaultScale(),
  }
}

// 🎯 导出常用的配置组合
export const COORDINATE_CONVERTER_CONFIG = {
  mapCenter: SceneConfig.getMapCenter(),
  modelRotate: SceneConfig.getModelRotation(),
  altitude: SceneConfig.getBaseAltitude(),
}

export const MAPBOX_CONVERTER_CONFIG = {
  modelOrigin: SceneConfig.getMapCenter(),
  modelAltitude: SceneConfig.getModelAltitude(),
  scale: SceneConfig.getDefaultScale(),
}

// 🔧 调试和验证函数
export function validateSceneConfig(): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // 验证地图中心坐标
  const [lng, lat] = SCENE_CONFIG.MAP_CENTER
  if (!SceneConfig.validateCoordinate(lng, lat)) {
    errors.push(`Invalid map center coordinates: [${lng}, ${lat}]`)
  }

  // 验证旋转角度
  const [rx, ry, rz] = SCENE_CONFIG.MODEL_ROTATE
  if (Math.abs(rx) > Math.PI * 2 || Math.abs(ry) > Math.PI * 2 || Math.abs(rz) > Math.PI * 2) {
    warnings.push(`Large rotation angles detected: [${rx}, ${ry}, ${rz}]`)
  }

  // 验证海拔高度
  if (SCENE_CONFIG.MODEL_ALTITUDE < -1000 || SCENE_CONFIG.MODEL_ALTITUDE > 10000) {
    warnings.push(`Unusual model altitude: ${SCENE_CONFIG.MODEL_ALTITUDE}m`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// 🎯 导出配置信息（用于调试）
export function getConfigInfo(): string {
  const config = SCENE_CONFIG
  const validation = validateSceneConfig()
  
  return `
场景配置信息:
- 地图中心: [${config.MAP_CENTER[0].toFixed(8)}, ${config.MAP_CENTER[1].toFixed(8)}]
- 模型海拔: ${config.MODEL_ALTITUDE}m

- 基准海拔: ${config.BASE_ALTITUDE}m
- 默认缩放: ${config.DEFAULT_ZOOM}

验证结果:
- 配置有效: ${validation.isValid ? '✅' : '❌'}
- 错误数量: ${validation.errors.length}
- 警告数量: ${validation.warnings.length}
${validation.errors.length > 0 ? '\n错误:\n' + validation.errors.map(e => `- ${e}`).join('\n') : ''}
${validation.warnings.length > 0 ? '\n警告:\n' + validation.warnings.map(w => `- ${w}`).join('\n') : ''}
`.trim()
}
