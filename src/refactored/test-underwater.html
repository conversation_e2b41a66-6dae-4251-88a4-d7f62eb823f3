<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水底贴图测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .feature h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .feature p {
            margin: 5px 0;
            color: #666;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌊 水底贴图功能已实现</h1>
        
        <div class="feature">
            <h3>✅ 核心功能</h3>
            <p>• 在水面下5米处创建水底贴图平面</p>
            <p>• 使用 /underwater.jpg 作为贴图纹理</p>
            <p>• 与水面尺寸保持一致</p>
            <p>• 支持透明度和双面渲染</p>
        </div>

        <div class="feature">
            <h3>🎛️ 控制方法</h3>
            <div class="code">
// 设置水底贴图透明度 (0-1)
waterSurface.setUnderwaterOpacity(0.8)

// 设置水底深度（米）
waterSurface.setUnderwaterDepth(5)

// 设置贴图重复次数
waterSurface.setUnderwaterTextureRepeat(2, 2)

// 获取水底网格对象
const underwaterMesh = waterSurface.getUnderwaterMesh()
            </div>
        </div>

        <div class="feature">
            <h3>🔧 集成功能</h3>
            <p>• 水底贴图会随水面一起显示/隐藏</p>
            <p>• 资源会在dispose()时自动清理</p>
            <p>• 支持贴图纹理的内存管理</p>
        </div>

        <div class="feature">
            <h3>📝 使用说明</h3>
            <p>1. WaterSurface类会自动创建水底贴图</p>
            <p>2. 贴图位置基于水面边界自动计算</p>
            <p>3. 可通过公共方法调整各种参数</p>
            <p>4. 贴图文件路径: <code>/underwater.jpg</code></p>
        </div>

        <div class="feature">
            <h3>🎨 视觉效果</h3>
            <p>• 透明度: 80% (可调节)</p>
            <p>• 材质: 双面渲染</p>
            <p>• 深度: 水面下5米 (可调节)</p>
            <p>• 重复: 1x1 (可调节)</p>
        </div>
    </div>
</body>
</html>