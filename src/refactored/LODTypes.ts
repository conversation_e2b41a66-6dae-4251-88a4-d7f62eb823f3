import * as THREE from "three"

/**
 * LOD级别定义
 */
export enum LODLevel {
	HIGH = "high", // 高精度模型
	MEDIUM = "medium", // 中精度模型
	LOW = "low", // 低精度模型
	HIDDEN = "hidden", // 隐藏
}

/**
 * LOD动画配置
 */
export interface LODAnimationConfig {
	enabled: boolean // 是否启用动画
	duration: number // 动画持续时间（毫秒）
	easing: "linear" | "ease-in" | "ease-out" | "ease-in-out" // 缓动函数
	fadeThreshold: number // 渐变阈值距离
}

/**
 * 简化的LOD配置
 */
export interface LODConfig {
	enabled: boolean // 是否启用LOD
	updateInterval: number // 更新间隔（毫秒）
	animation: LODAnimationConfig // 动画配置
}

/**
 * 默认LOD配置
 */
export const DEFAULT_LOD_CONFIG: LODConfig = {
	enabled: true,
	updateInterval: 100,
	animation: {
		enabled: true,
		duration: 300,
		easing: "ease-out",
		fadeThreshold: 20,
	},
}

/**
 * LOD动画状态
 */
export interface LODAnimationState {
	isAnimating: boolean // 是否正在动画
	startTime: number // 动画开始时间
	duration: number // 动画持续时间
	fromOpacity: number // 起始透明度
	toOpacity: number // 目标透明度
	fromLevel: LODLevel // 起始LOD级别
	toLevel: LODLevel // 目标LOD级别
	easing: string // 缓动函数
}

/**
 * LOD模型数据
 */
export interface LODModelData {
	id: string
	name: string
	position: THREE.Vector3

	// 不同精度的模型路径
	models: {
		high?: string // 高精度模型路径
		medium?: string // 中精度模型路径
		low?: string // 低精度模型路径
	}

	// 当前状态
	currentLevel: LODLevel
	distanceToCamera: number
	isVisible: boolean
	isLoading: boolean

	// Three.js对象
	objects: {
		high?: THREE.Object3D
		medium?: THREE.Object3D
		low?: THREE.Object3D
	}

	// 元数据
	boundingBox?: THREE.Box3
	priority: number // 加载优先级
	lastUpdateTime: number

	// 🎬 动画相关属性
	animationState?: LODAnimationState // 动画状态
	targetOpacity: number // 目标透明度
	currentOpacity: number // 当前透明度

	// 建筑相关元数据
	metadata?: {
		isExterior?: boolean
		isInterior?: boolean
		buildingId?: string
		floor?: number
		isDefault?: boolean
		hasInterior?: boolean
		switchDistance?: number
		fadeDistance?: number
		hasParent?: boolean
	}
}

/**
 * LOD管理器接口
 */
export interface LODManager {
	// 配置管理
	setConfig(config: Partial<LODConfig>): void
	getConfig(): LODConfig
	setEnabled(enabled: boolean): void
	isEnabled(): boolean

	// 模型管理
	// addModel(
	// 	modelData: Omit<
	// 		LODModelData,
	// 		| "currentLevel"
	// 		| "distanceToCamera"
	// 		| "isVisible"
	// 		| "isLoading"
	// 		| "objects"
	// 		| "lastUpdateTime"
	// 	>
	// ): string
	addModelWithObject?(modelData: {
		id: string
		name: string
		position: THREE.Vector3
		object: THREE.Object3D
		priority?: number
	}): string
	removeModel(id: string): void
	getModel(id: string): LODModelData | undefined
	getAllModels(): LODModelData[]

	// LOD控制
	updateLOD(camera: THREE.Camera): void
	setModelLevel(id: string, level: LODLevel): void
	getModelLevel(id: string): LODLevel

	// 距离计算
	determineLODLevel(distance: number): LODLevel

	// 模型加载
	loadModel(id: string, level: LODLevel): Promise<THREE.Object3D>
	preloadModels(ids: string[], level: LODLevel): Promise<void>

	// 性能监控
	getPerformanceStats(): LODPerformanceStats

	// 🔧 调试和诊断方法
	diagnoseLODState?(): void
	fixAllModelStates?(): void
	testOpacity?(modelId: string, opacity: number): void
	testAnimation?(
		modelId: string,
		fromOpacity: number,
		toOpacity: number,
		duration: number
	): void
	debugModelMaterials?(modelId: string): void

	// 事件
	on(event: string, handler: (...args: any[]) => void): void
	off(event: string, handler: (...args: any[]) => void): void
	emit(event: string, ...args: any[]): void

	// 清理
	dispose(): void
}

/**
 * LOD性能统计
 */
export interface LODPerformanceStats {
	totalModels: number
	visibleModels: number
	loadingModels: number

	levelCounts: {
		high: number
		medium: number
		low: number
		hidden: number
	}

	memoryUsage: {
		estimated: number // 估计内存使用（MB）
		triangles: number // 总三角形数
	}

	performance: {
		lastUpdateTime: number
		averageUpdateTime: number
		frameRate: number
	}
}

/**
 * LOD事件类型
 */
export enum LODEventType {
	LEVEL_CHANGED = "level-changed",
	MODEL_LOADED = "model-loaded",
	MODEL_UNLOADED = "model-unloaded",
	MODEL_ADDED = "model-added",
	MODEL_REMOVED = "model-removed",
	VISIBILITY_CHANGED = "visibility-changed",
	CONFIG_CHANGED = "config-changed",
	PERFORMANCE_UPDATE = "performance-update",
	LOD_UPDATED = "lod-updated",
	LOD_BUILDING_ENTERED = "lod-building-entered",
	LOD_BUILDING_EXITED = "lod-building-exited",
	LOD_BUILDING_FLOOR_CHANGED = "lod-building-floor-changed",
	// UI控制事件
	FLOOR_PANEL_SHOW = "floor-panel-show",
	FLOOR_PANEL_HIDE = "floor-panel-hide",
}

/**
 * LOD事件数据
 */
export interface LODEventData {
	modelId?: string
	oldLevel?: LODLevel
	newLevel?: LODLevel
	distance?: number
	loadTime?: number
	error?: Error
	stats?: LODPerformanceStats
}

/**
 * 工具函数：计算两点间距离
 */
export function calculateDistance3D(
	pos1: THREE.Vector3,
	pos2: THREE.Vector3
): number {
	return pos1.distanceTo(pos2)
}

/**
 * 工具函数：计算包围盒大小
 */
export function calculateBoundingBoxSize(box: THREE.Box3): number {
	const size = new THREE.Vector3()
	box.getSize(size)
	return Math.max(size.x, size.y, size.z)
}

/**
 * 工具函数：估算模型复杂度
 */
export function estimateModelComplexity(object: THREE.Object3D): number {
	let triangles = 0

	object.traverse((child) => {
		if (child instanceof THREE.Mesh && child.geometry) {
			const geometry = child.geometry
			if (geometry.index) {
				triangles += geometry.index.count / 3
			} else if (geometry.attributes.position) {
				triangles += geometry.attributes.position.count / 3
			}
		}
	})

	return triangles
}

/**
 * 工具函数：创建LOD模型数据
 */
export function createLODModelData(
	id: string,
	name: string,
	position: THREE.Vector3,
	models: {
		high?: string
		medium?: string
		low?: string
	},
	options: {
		priority?: number
		boundingBox?: THREE.Box3
	} = {}
): Omit<
	LODModelData,
	| "currentLevel"
	| "distanceToCamera"
	| "isVisible"
	| "isLoading"
	| "objects"
	| "lastUpdateTime"
> {
	return {
		id,
		name,
		position: position.clone(),
		models,
		priority: options.priority || 0,
		boundingBox: options.boundingBox,
		// 🎬 动画相关属性
		targetOpacity: 1.0,
		currentOpacity: 1.0,
	}
}

/**
 * 工具函数：格式化性能统计
 */
export function formatPerformanceStats(stats: LODPerformanceStats): string {
	return `
LOD Performance Stats:
- Total Models: ${stats.totalModels}
- Visible Models: ${stats.visibleModels}
- Loading Models: ${stats.loadingModels}
- Level Distribution: H:${stats.levelCounts.high} M:${
		stats.levelCounts.medium
	} L:${stats.levelCounts.low} Hidden:${stats.levelCounts.hidden}
- Memory Usage: ${stats.memoryUsage.estimated.toFixed(
		1
	)}MB (${stats.memoryUsage.triangles.toLocaleString()} triangles)
- Performance: ${stats.performance.frameRate.toFixed(
		1
	)}fps, Update: ${stats.performance.averageUpdateTime.toFixed(1)}ms
  `.trim()
}
