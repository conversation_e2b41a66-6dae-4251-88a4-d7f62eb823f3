import mapboxgl from "mapbox-gl"
import "mapbox-gl/dist/mapbox-gl.css"
import * as THREE from "three"
import { eventBus } from "@/refactored/EventBus"
import { LayerManager } from "./LayerManager"
import { DefaultModelTreeManager } from "./ModelTreeManager"
import type { ModelTreeManager } from "./ModelTypes"
import { gsap } from "gsap"

// import tiandituConfig from "./tiandituConfig"
import { SceneConfig } from "./SceneConfig"
// import Stats from "three/examples/jsm/libs/stats.module.js"
import { WaterSurface } from "./WaterSurface"
import { AccurateCoordinateConverter } from "./MapboxCoordinateConverter"
import { GeojsonManager } from "./GeojsonManger"

import GeoJSONUrl from "/markers.geojson?url"
import { NavigationManager } from "./NavigationManager"
import { CameraController } from "./cameraControler"
import { TimeSystem, type TimeSystemConfig, type SunPosition, type SkyColors } from "./TimeSystem"
import { RenderManager } from "./RenderManager"
import { getLightingConfig, type CompleteLightingConfig, LightingTransitionManager } from "./LightingConfig"

//mapbox key
const MAPBOXKEY =
	"pk.eyJ1Ijoic3ZjLW9rdGEtbWFwYm94LXN0YWZmLWFjY2VzcyIsImEiOiJjbG5sMnExa3kxNTJtMmtsODJld24yNGJlIn0.RQ4CHchAYPJQZSiUJ0O3VQ"
//🔧 统一：使用场景配置中的统一参数
const mapCenter: [number, number] = SceneConfig.getMapCenter()
const modelAltitude = SceneConfig.getModelAltitude()
const modelRotate = SceneConfig.getModelRotation()
const modelAsMercatorCoordinate = mapboxgl.MercatorCoordinate.fromLngLat(
	mapCenter,
	modelAltitude
)

const modelTransform = {
	translateX: modelAsMercatorCoordinate.x,
	translateY: modelAsMercatorCoordinate.y,
	translateZ: modelAsMercatorCoordinate.z,
	rotateX: modelRotate[0],
	rotateY: modelRotate[1],
	rotateZ: modelRotate[2],
	/* Since the 3D model is in real world meters, a scale transform needs to be
	 * applied since the CustomLayerInterface expects units in MercatorCoordinates.
	 */
	scale: modelAsMercatorCoordinate.meterInMercatorCoordinateUnits(),
}

//配置mapboxtoken
mapboxgl.accessToken = MAPBOXKEY

//基础配置
export interface ApplicationConfig {
	//地图中心点
	defaultCenter?: [number, number]
	//地图缩放
	defaultZoom?: number
	//地图容器ID / 元素
	container: string | HTMLElement
	//时间系统配置
	timeSystem?: TimeSystemConfig
}

//程序基底 采用Mapboxgl + threejs 实现导航+3D模型的功能
export default class MapApplication {
	//地图
	public map: mapboxgl.Map
	//three 场景
	public scene?: THREE.Scene
	//相机
	public camera?: THREE.PerspectiveCamera
	//渲染器
	public renderer?: THREE.WebGLRenderer
	//射线
	public raycaster?: THREE.Raycaster
	//鼠标
	public mouse?: THREE.Vector2
	//地图加载Promise
	public mapLoadPromise: Promise<void>
	//three加载Promise
	public threeLoadPromise?: Promise<void>
	private treeManager: ModelTreeManager
	private layerManager?: LayerManager
	private geojsonManager?: GeojsonManager
	private waterSurface?: WaterSurface
	private cameraController?: CameraController
	private clickCallbacks: Array<
		(event: {
			x: number
			y: number
			originalEvent: PointerEvent
			world: THREE.Vector3 | null
		}) => void
	> = []
	private mouseMoveCallbacks: Array<
		(event: {
			x: number
			y: number
			originalEvent: MouseEvent
			world: THREE.Vector3 | null
		}) => void
	> = []
	private renderCallbacks: Array<(camera: THREE.Camera) => void> = []
	public threeLayerReadyPromise!: Promise<void>
	private _threeLayerReadyResolve!: () => void
	private NavigationManager?: NavigationManager
	private timeSystem?: TimeSystem
	private renderManager: RenderManager

	// 🎮 独立动画循环
	private animationId: number | null = null
	private isAnimationRunning: boolean = false

	// 🎬 光照过渡管理器
	private lightingTransitionManager: LightingTransitionManager
	private lastLightingConfig: CompleteLightingConfig | null = null

	// 保存相机位置用于恢复
	private savedCameraPosition: THREE.Vector3 | null = null
	// 缓存上次的地图亮度值，避免频繁更新
	private lastMapBrightness: number | null = null

	constructor(config: ApplicationConfig) {
		// 初始化树管理器
		this.treeManager = new DefaultModelTreeManager()
		// 初始化渲染管理器
		this.renderManager = new RenderManager()
		// 🎬 初始化光照过渡管理器
		this.lightingTransitionManager = new LightingTransitionManager()

		this.map = new mapboxgl.Map({
			container: config.container || "map",
			// 🌌 使用支持FOG和天空效果的样式
			style: "mapbox://styles/mapbox/standard", // 改用Standard样式支持FOG
			center: (config.defaultCenter || mapCenter) as [number, number],
			zoom: config.defaultZoom || 18,
			maxZoom: 22,
			// 🔍 临时注释相机限制，方便查找太阳、月亮、星星
			// minPitch: 0,
			// maxPitch: 80,
		})

		let resolve: () => void
		this.mapLoadPromise = new Promise<void>((res) => {
			resolve = res
		})
		this.geojsonManager = new GeojsonManager(GeoJSONUrl, this.map)
		this.cameraController = new CameraController(this.map)
		// 🚀 初始化时间系统 - 优化光照同步
		if (config.timeSystem) {
			this.timeSystem = new TimeSystem(config.timeSystem)

			// 🌌 统一的光照更新回调 - 解决执行顺序延迟问题
			this.timeSystem.onTimeUpdate((time: Date, sunPosition: SunPosition) => {
				// 只更新UI显示，不触发光照更新
				console.log(`⏰ 时间更新: ${time.toLocaleTimeString()}`)
			})

			// 🌌 环境光照统一更新 - 同时使用新旧系统
			this.timeSystem.onSkyColorChange((skyColors: SkyColors) => {
				// 获取当前太阳位置和时间
				const lightingState = this.timeSystem.getCurrentLightingState()
				if (lightingState && this.timeSystem) {
					console.log('🌌 开始统一光照系统更新...')

					// 🎨 1. 使用新的统一光照配置系统
					const currentTime = this.timeSystem.getCurrentTime()
					const newLightingConfig = getLightingConfig(currentTime)
					this.applyCompleteLightingConfig(newLightingConfig, lightingState.sunPosition)

					// 🌈 2. 同时调用原有的天空颜色更新方法（确保FOG效果正确）
					this.updateSkyColors(skyColors)

					// 🗺️ 3. 调用地图亮度更新
					this.updateMapBrightness(lightingState.mapBrightness)

					// 强制重绘
					this.repaint(true)

					console.log('✅ 统一光照系统更新完成')
				}
			})

			// 地图亮度更新已经在上面统一处理，这里不再单独处理
			this.timeSystem.onMapBrightnessChange((brightness: number) => {
				// 空实现，避免重复更新
			})
		}

		// Mapbox底图加载
		this.map.once("load", () => {
			// 🏢 关闭Mapbox 3D建筑模型
			this.disableMapbox3DBuildings()
			// 添加天空层
			this.addSkyLayer()
			// 🚀 启动时间系统并应用初始光照
			if (this.timeSystem) {
				this.timeSystem.start()
				// 延迟应用初始光照，确保天空层已添加
				setTimeout(() => {
					this.timeSystem?.applyInitialLighting()
				}, 500)
			}
			// 🎮 启动独立动画循环
			this.startAnimationLoop()
			eventBus.emit("scene-progress", 0.2)
			resolve()
		})

		this.map.on("rotate", (event) => {
			eventBus.emit("rotate", event)
		})

		eventBus.on("reset-rotate", () => {
			this.map.rotateTo(0)
		})

		eventBus.on("repaint", () => {
			this.map.triggerRepaint()
		})
	}

	//获取地图实例
	public getMap(): mapboxgl.Map {
		return this.map
	}

	/**
	 * 设置灯光和阴影系统
	 */
	private setupLightingAndShadows(): void {
		// 环境光 - 设置较低的初始强度，主要由时间系统动态控制
		const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
		this.scene!.add(ambientLight)

		// 主方向光 - 投射阴影，设置适中的初始强度
		const dirLight = new THREE.DirectionalLight(0xffffff, 0.5)
		dirLight.position.set(-30, 100, -100)
		dirLight.castShadow = true
		const d2 = 1000
		const r2 = 2
		const mapSize2 = 8192
		// 阴影设置 - 优化以减少摩尔纹
		dirLight.castShadow = true
		dirLight.shadow.radius = r2
		dirLight.shadow.mapSize.width = mapSize2
		dirLight.shadow.mapSize.height = mapSize2
		dirLight.shadow.camera.top = dirLight.shadow.camera.right = d2
		dirLight.shadow.camera.bottom = dirLight.shadow.camera.left = -d2
		dirLight.shadow.camera.near = 1
		dirLight.shadow.camera.visible = true
		dirLight.shadow.camera.far = 400000000

		// 减少阴影偏移和摩尔纹
		// dirLight.shadow.bias = -0.0005
		// dirLight.shadow.normalBias = 0.02
		dirLight.updateMatrixWorld()
		this.scene!.add(dirLight)
	}

	/**
	 * 设置阴影渲染器
	 */
	private setupShadowRenderer(): void {
		if (!this.renderer) return

		// 启用阴影
		this.renderer.shadowMap.enabled = true
		// this.renderer.shadowMap.type = THREE.PCFSoftShadowMap

		// 减少摩尔纹的设置
		this.renderer.shadowMap.autoUpdate = true
		this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

		// 启用更好的深度测试
		// this.renderer.sortObjects = true
	}

	//获取树管理器
	public getTreeManager(): ModelTreeManager {
		return this.treeManager
	}

	//获取图层管理器
	public getLayerManager(): LayerManager | undefined {
		return this.layerManager
	}

	//加载three图层
	public addThreeJsLayer() {
		this.threeLayerReadyPromise = new Promise<void>((resolve) => {
			this._threeLayerReadyResolve = resolve
		})

		let threeLoadResolve: () => void
		this.threeLoadPromise = new Promise<void>((res) => {
			threeLoadResolve = res
		})
		// const stats = new Stats()
		const customLayer = {
			id: "threejs-layer",
			type: "custom" as const,
			renderingMode: "3d" as const,
			onAdd: (map: mapboxgl.Map, gl: WebGLRenderingContext) => {
				this.scene = new THREE.Scene()
				this.layerManager = new LayerManager(
					this.scene,
					this.treeManager
				)
				this.camera = new THREE.PerspectiveCamera()
				this.raycaster = new THREE.Raycaster()
				this.mouse = new THREE.Vector2()
				this.NavigationManager = new NavigationManager(this.scene)
				// stats.showPanel(0)
				// document.body.appendChild(stats.dom)
				// 点击事件
				map.getCanvas().addEventListener(
					"pointerdown",
					(event: PointerEvent) => {
						this.raycast(event)
					}
				)

				// 鼠标移动事件
				// map.getCanvas().addEventListener(
				// 	"mousemove",
				// 	(event: MouseEvent) => {
				// 		this.mousemove(event)
				// 	}
				// )

				// 添加灯光和阴影系统
				this.setupLightingAndShadows()
				this.setupShadowRenderer()
				this.renderer = new THREE.WebGLRenderer({
					canvas: map.getCanvas(),
					context: gl,
					antialias: true,
				})
				this.renderer.autoClear = false
				// 启用高质量阴影
				// this.setupShadowRenderer()

				// 在场景中存储渲染器引用，供水面使用
				if (this.scene) {
					this.scene.userData.renderer = this.renderer
				}

				// 创建水面
				this.createWaterSurface()

				// 设置RenderManager的渲染组件
				this.renderManager.setRenderComponents(this.scene, this.camera, this.renderer)
				
				// 将水面更新添加到RenderManager
				if (this.waterSurface) {
					this.renderManager.addRenderCallback((camera) => {
						this.waterSurface?.update(camera)
					})
				}
				
				// 将现有的渲染回调迁移到RenderManager
				this.renderCallbacks.forEach(callback => {
					this.renderManager.addRenderCallback(callback)
				})

				this._threeLayerReadyResolve() // 初始化完成
				threeLoadResolve!()
			},
			render: (_gl: WebGLRenderingContext, matrix: number[]) => {
				// stats.begin()
				const rotationX = new THREE.Matrix4().makeRotationAxis(
					new THREE.Vector3(1, 0, 0),
					modelTransform.rotateX
				)
				const rotationY = new THREE.Matrix4().makeRotationAxis(
					new THREE.Vector3(0, 1, 0),
					modelTransform.rotateY
				)
				const rotationZ = new THREE.Matrix4().makeRotationAxis(
					new THREE.Vector3(0, 0, 1),
					modelTransform.rotateZ
				)

				const m = new THREE.Matrix4().fromArray(matrix)
				const l = new THREE.Matrix4()
					.makeTranslation(
						modelTransform.translateX,
						modelTransform.translateY,
						modelTransform.translateZ
					)
					.scale(
						new THREE.Vector3(
							modelTransform.scale,
							-modelTransform.scale,
							modelTransform.scale
						)
					)
					.multiply(rotationZ)
					.multiply(rotationX)
					.multiply(rotationY)
				this.camera!.projectionMatrix = m.multiply(l)
				this.renderer!.resetState()

				// 🚀 快速循环：执行所有渲染回调（包括水面和鱼动画）
				// 1. 🌊 直接更新水面和鱼动画（最高优先级，确保执行）
				if (this.waterSurface) {
					try {
						this.waterSurface.update(this.camera!)
						// console.log("🌊 水面动画已更新")
					} catch (error) {
						console.warn("🌊 水面更新失败:", error)
					}
				}

				// 2. 执行原有的渲染回调
				this.renderCallbacks.forEach(callback => {
					try {
						callback(this.camera!)
					} catch (error) {
						console.warn("渲染回调执行失败:", error)
					}
				})

				// 渲染Three.js场景
				this.renderer!.render(this.scene!, this.camera!)
				this.NavigationManager!.updateScale(this.camera!)

				// stats.end()
			},
		}

		this.map.addLayer(customLayer) //添加图层
		console.log("添加三维图层完成")
	}

	/** 注册地图点击回调 */
	public addClickCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: PointerEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.clickCallbacks.push(cb)
	}

	/** 注销地图点击回调 */
	public removeClickCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: PointerEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.clickCallbacks = this.clickCallbacks.filter((fn) => fn !== cb)
	}

	/** 注册鼠标移动回调 */
	public addMouseMoveCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: MouseEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.mouseMoveCallbacks.push(cb)
	}

	/** 注册渲染回调 */
	public addRenderCallback(callback: (camera: THREE.Camera) => void) {
		// 将回调添加到RenderManager
		this.renderManager.addRenderCallback(callback)
		// 保持向后兼容性，也添加到本地数组
		this.renderCallbacks.push(callback)
	}

	/** 注销鼠标移动回调 */
	public removeMouseMoveCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: MouseEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.mouseMoveCallbacks = this.mouseMoveCallbacks.filter(
			(fn) => fn !== cb
		)
	}

	//销毁
	public destroy() {
		this.layerManager?.dispose()
		this.renderer?.dispose()
		this.scene?.clear()
		this.camera?.clear()
		if (this.timeSystem) {
			this.timeSystem.dispose()
		}
		
		// 销毁RenderManager
		this.renderManager.dispose()
		
		this.map.remove()
	}

	raycast(event: PointerEvent) {
		var mouse = new THREE.Vector2()
		// scale mouse pixel position to a percentage of the screen's width and height
		const rect = this.map.getCanvas().getBoundingClientRect()
		const x = event.clientX - rect.left
		const y = event.clientY - rect.top
		mouse.x = (x / this.map.transform.width) * 2 - 1
		mouse.y = 1 - (y / this.map.transform.height) * 2

		const camInverseProjection = this.camera?.projectionMatrix
			.clone()
			.invert()
		const cameraPosition = new THREE.Vector3().applyMatrix4(
			camInverseProjection!
		)
		const mousePosition = new THREE.Vector3(
			mouse.x,
			mouse.y,
			1
		).applyMatrix4(camInverseProjection!)
		const viewDirection = mousePosition
			.clone()
			.sub(cameraPosition)
			.normalize()

		this.raycaster!.set(cameraPosition, viewDirection)

		// calculate objects intersecting the picking ray
		let pickObjects = this.scene!.children
		var intersects = this.raycaster!.intersectObjects(pickObjects, true)
		if (intersects.length) {
			const target = intersects[0].point
			console.log("点击位置:", intersects)

			//
			const convert = new AccurateCoordinateConverter(
				SceneConfig.getMapCenter(),
				SceneConfig.getModelRotation(),
				SceneConfig.getBaseAltitude()
			)

			const coord = convert.worldToGeo(target)
			console.log(coord);



			this.clickCallbacks.forEach((cb) =>
				cb({
					x: target.x,
					y: target.y,
					originalEvent: event,
					world: target,
				})
			)
		}
	}

	mousemove(event: MouseEvent) {
		var mouse = new THREE.Vector2()
		// scale mouse pixel position to a percentage of the screen's width and height
		const rect = this.map.getCanvas().getBoundingClientRect()
		const x = event.clientX - rect.left
		const y = event.clientY - rect.top
		mouse.x = (x / this.map.transform.width) * 2 - 1
		mouse.y = 1 - (y / this.map.transform.height) * 2

		const camInverseProjection = this.camera?.projectionMatrix
			.clone()
			.invert()
		const cameraPosition = new THREE.Vector3().applyMatrix4(
			camInverseProjection!
		)
		const mousePosition = new THREE.Vector3(
			mouse.x,
			mouse.y,
			1
		).applyMatrix4(camInverseProjection!)
		const viewDirection = mousePosition
			.clone()
			.sub(cameraPosition)
			.normalize()

		this.raycaster!.set(cameraPosition, viewDirection)

		// calculate objects intersecting the picking ray
		let pickObjects = this.scene!.children
		var intersects = this.raycaster!.intersectObjects(pickObjects, true)
		if (intersects.length) {
			const target = intersects[0].point
			this.mouseMoveCallbacks.forEach((cb) =>
				cb({
					x: target.x,
					y: target.y,
					originalEvent: event,
					world: target,
				})
			)
		}
	}

	/**
	 * 创建水面
	 */
	private createWaterSurface(): void {
		if (!this.scene) {
			console.warn("⚠️ 场景未初始化，无法创建水面")
			return
		}

		try {
			this.waterSurface = new WaterSurface(this.scene)
			console.log("🌊 水面创建成功")
		} catch (error) {
			console.error("❌ 水面创建失败:", error)
		}
	}

	/**
	 * 获取水面实例
	 */
	public getWaterSurface(): WaterSurface | undefined {
		return this.waterSurface
	}

	/**
	 * 设置水面可见性
	 */
	public setWaterVisible(visible: boolean): void {
		if (this.waterSurface) {
			this.waterSurface.setVisible(visible)
		}
	}

	/**
	 * 设置水面透明度
	 */
	public setWaterOpacity(opacity: number): void {
		if (this.waterSurface) {
			this.waterSurface.setOpacity(opacity)
		}
	}

	/**
	 * 设置水面噪波强度
	 */
	public setWaterDistortion(scale: number): void {
		if (this.waterSurface) {
			this.waterSurface.setDistortionScale(scale)
		}
	}

	/**
	 * 关闭水面噪波
	 */
	public disableWaterDistortion(): void {
		if (this.waterSurface) {
			this.waterSurface.disableDistortion()
		}
	}

	/**
	 * 轻量级重绘：仅更新必要的渲染内容
	 * 避免频繁的全场景重绘
	 */
	public repaint(forceFullRender: boolean = false) {
		try {
			if (forceFullRender) {
				// 完整重绘：Mapbox + Three.js（仅在环境变化时）
				this.map.triggerRepaint()
			} else {
				// 轻量级重绘：使用RenderManager
				this.renderManager.forceRender()
			}
		} catch (error) {
			console.warn("重绘失败:", error)
		}
	}

	/**
	 * 设置Mapbox底图可见性
	 */
	public setMapboxVisible(visible: boolean): void {
		try {
			if (visible) {
				// 显示底图：移除所有图层的隐藏样式
				const style = this.map.getStyle()
				if (style && style.layers) {
					style.layers.forEach((layer: any) => {
						if (
							layer.layout &&
							layer.layout.visibility === "none"
						) {
							this.map.setLayoutProperty(
								layer.id,
								"visibility",
								"visible"
							)
						}
					})
				}
				console.log(`🗺️ Mapbox底图显示`)
			} else {
				// 隐藏底图：隐藏所有图层但保持Three.js场景
				const style = this.map.getStyle()
				if (style && style.layers) {
					style.layers.forEach((layer: any) => {
						this.map.setLayoutProperty(
							layer.id,
							"visibility",
							"none"
						)
					})
				}
				console.log(`🗺️ Mapbox底图隐藏`)
			}
		} catch (error) {
			console.warn("⚠️ 设置Mapbox可见性失败:", error)
			// 降级方案：使用容器透明度，但只针对地图容器
			const container = this.map.getContainer()
			if (container) {
				const mapContainer = container.querySelector(
					".mapboxgl-map"
				) as HTMLElement
				if (mapContainer) {
					mapContainer.style.opacity = visible ? "1" : "0"
				}
			}
		}
	}

	/**
	 * 设置拆楼模式相机控制
	 */
	public setExplodedCameraMode(enabled: boolean, buildingId?: string): void {
		try {
			if (enabled && buildingId) {
				// 启用拆楼模式：保留旋转，禁用平移和缩放
				console.log(`🎥 启用拆楼模式相机控制: ${buildingId}`)

				// 禁用地图的平移和缩放，但保留旋转
				this.map.dragPan.disable() // 禁用拖拽平移
				this.map.scrollZoom.disable() // 禁用滚轮缩放
				this.map.boxZoom.disable() // 禁用框选缩放
				this.map.keyboard.disable() // 禁用键盘控制
				this.map.doubleClickZoom.disable() // 禁用双击缩放
				this.map.touchZoomRotate.disable() // 禁用触摸缩放

				// 保留旋转功能
				this.map.dragRotate.enable() // 保持拖拽旋转

				console.log(`🔒 拆楼模式：已禁用平移和缩放，保留旋转功能`)
			} else {
				// 禁用拆楼模式：恢复正常相机控制
				console.log(`🎥 禁用拆楼模式相机控制`)

				// 恢复地图的所有交互
				this.map.dragPan.enable()
				this.map.scrollZoom.enable()
				this.map.boxZoom.enable()
				this.map.dragRotate.enable()
				this.map.keyboard.enable()
				this.map.doubleClickZoom.enable()
				this.map.touchZoomRotate.enable()

				console.log(`🔓 拆楼模式退出：已恢复所有地图交互`)
			}
		} catch (error) {
			console.warn("⚠️ 设置拆楼模式相机控制失败:", error)
		}
	}

	/**
	 * 拆楼模式下调整相机高度到指定楼层
	 */
	public adjustCameraHeightInExplodedMode(
		targetHeight: number,
		targetFloor: number
	): void {
		try {
			const camera = this.camera
			if (!camera) {
				console.warn("⚠️ 相机不可用，无法调整高度")
				return
			}

			console.log(
				`🎥 调整相机高度到楼层 ${targetFloor}F, 目标高度: ${targetHeight}`
			)

			// 使用GSAP平滑移动相机到目标高度
			const currentPosition = camera.position.clone()
			const targetPosition = currentPosition.clone()
			targetPosition.y = targetHeight + 20 // 在楼层上方20单位的高度

			// 创建相机移动动画
			gsap.to(camera.position, {
				y: targetPosition.y,
				duration: 0.5, // 0.5秒的平滑移动
				ease: "power2.inOut",
				onUpdate: () => {
					// 每帧更新相机矩阵
					camera.updateMatrixWorld()
					this.repaint()
				},
				onComplete: () => {
					console.log(
						`✅ 相机已移动到楼层 ${targetFloor}F 高度: ${targetPosition.y}`
					)
				},
			})
		} catch (error) {
			console.warn("⚠️ 调整拆楼模式相机高度失败:", error)
		}
	}

	/**
	 * 保存当前相机位置
	 */
	public saveCameraPosition(): void {
		try {
			const camera = this.camera
			if (!camera) {
				console.warn("⚠️ 相机不可用，无法保存位置")
				return
			}

			this.savedCameraPosition = camera.position.clone()
			console.log(`💾 已保存相机位置:`, {
				x: this.savedCameraPosition.x,
				y: this.savedCameraPosition.y,
				z: this.savedCameraPosition.z,
			})
		} catch (error) {
			console.warn("⚠️ 保存相机位置失败:", error)
		}
	}

	/**
	 * 恢复相机位置到保存的状态
	 */
	public restoreCameraPosition(): void {
		try {
			const camera = this.camera
			if (!camera) {
				console.warn("⚠️ 相机不可用，无法恢复位置")
				return
			}

			if (!this.savedCameraPosition) {
				console.warn("⚠️ 没有保存的相机位置")
				return
			}

			console.log(`🔄 恢复相机位置:`, {
				from: {
					x: camera.position.x,
					y: camera.position.y,
					z: camera.position.z,
				},
				to: {
					x: this.savedCameraPosition.x,
					y: this.savedCameraPosition.y,
					z: this.savedCameraPosition.z,
				},
			})

			// 使用GSAP平滑恢复相机位置
			gsap.to(camera.position, {
				x: this.savedCameraPosition.x,
				y: this.savedCameraPosition.y,
				z: this.savedCameraPosition.z,
				duration: 0.8, // 0.8秒的平滑移动
				ease: "power2.inOut",
				onUpdate: () => {
					// 每帧更新相机矩阵
					camera.updateMatrixWorld()
					this.repaint()
				},
				onComplete: () => {
					console.log(`✅ 相机位置已恢复`)
					// 清除保存的位置
					this.savedCameraPosition = null
				},
			})
		} catch (error) {
			console.warn("⚠️ 恢复相机位置失败:", error)
		}
	}

	/**
	 * 🏢 关闭Mapbox 3D建筑模型
	 * 解决Standard样式带来的白膜问题
	 */
	private disableMapbox3DBuildings(): void {
		try {
			// 等待样式加载完成
			if (!this.map.isStyleLoaded()) {
				this.map.once('styledata', () => this.disableMapbox3DBuildings())
				return
			}

			// 🏢 移除或隐藏3D建筑图层
			const style = this.map.getStyle()
			if (style && style.layers) {
				// 查找并移除3D建筑相关图层
				const buildingLayers = style.layers.filter((layer: any) =>
					layer.id && (
						layer.id.includes('building') ||
						layer.id.includes('3d') ||
						layer.type === 'fill-extrusion'
					)
				)

				buildingLayers.forEach((layer: any) => {
					try {
						// 设置为不可见而不是移除，避免样式错误
						this.map.setLayoutProperty(layer.id, 'visibility', 'none')
						console.log(`🏢 已隐藏3D建筑图层: ${layer.id}`)
					} catch (error) {
						console.warn(`隐藏图层 ${layer.id} 失败:`, error)
					}
				})
			}

			// 🎨 如果使用Standard样式，尝试关闭3D建筑配置
			try {
				// Standard样式的3D建筑配置
				this.map.setConfigProperty('basemap', 'show3dObjects', false)
				console.log('🏢 已关闭Standard样式3D对象')
			} catch (error) {
				console.log('当前样式不支持3D对象配置')
			}

			console.log('🏢 Mapbox 3D建筑模型已关闭')
		} catch (error) {
			console.warn("关闭Mapbox 3D建筑失败:", error)
		}
	}

	/**
	 * 🎮 启动独立动画循环
	 * 确保水面和鱼群动画持续运行，不依赖地图交互
	 */
	private startAnimationLoop(): void {
		if (this.isAnimationRunning) return

		this.isAnimationRunning = true

		const animate = () => {
			if (!this.isAnimationRunning) return

			try {
				// 🌊 执行水面和鱼群动画更新
				if (this.waterSurface && this.camera) {
					this.waterSurface.update(this.camera)
				}

				// 🎮 执行所有渲染回调
				if (this.camera) {
					this.renderCallbacks.forEach(callback => {
						try {
							callback(this.camera!)
						} catch (error) {
							console.warn("动画回调执行失败:", error)
						}
					})
				}

				// 🔄 触发Mapbox重绘以显示动画
				this.map.triggerRepaint()

			} catch (error) {
				console.warn("动画循环执行失败:", error)
			}

			// 继续下一帧
			this.animationId = requestAnimationFrame(animate)
		}

		// 启动动画循环
		this.animationId = requestAnimationFrame(animate)
		console.log("🎮 独立动画循环已启动")
	}

	/**
	 * 🛑 停止独立动画循环
	 */
	private stopAnimationLoop(): void {
		this.isAnimationRunning = false
		if (this.animationId) {
			cancelAnimationFrame(this.animationId)
			this.animationId = null
		}
		console.log("🛑 独立动画循环已停止")
	}

	/**
	 * 🌌 添加Mapbox天空层和FOG效果
	 */
	private addSkyLayer(): void {
		try {
			// 等待地图样式加载完成
			if (!this.map.isStyleLoaded()) {
				this.map.once('styledata', () => this.addSkyLayer())
				return
			}

			// 🌌 添加大气天空层（支持太阳、月亮、星星）
			this.map.addLayer({
				id: "sky",
				type: "sky",
				paint: {
					// 使用大气天空类型，支持太阳和星星
					"sky-type": "atmosphere",
					// 🌞 太阳位置（初始设置为可见位置）
					"sky-atmosphere-sun": [180.0, 45.0], // 南方45度
					// 太阳强度
					"sky-atmosphere-sun-intensity": 25,
					// 天空不透明度
					"sky-opacity": 1
				}
			})

			// 🌫️ 添加FOG效果 - 使用Standard样式支持的参数
			this.map.setFog({
				"color": "rgb(186, 210, 235)", // 使用rgb格式而不是rgba
				"horizon-blend": 0.15,
				"range": [1.0, 12] as [number, number],
				"high-color": "rgb(135, 206, 250)",
				"space-color": "rgb(11, 11, 25)",
				"star-intensity": 0.0
			})

			// 🎨 应用初始光照配置
			const initialTime = new Date()
			const initialConfig = getLightingConfig(initialTime)
			console.log(`🎨 应用初始光照配置 - 时段: ${initialTime.getHours()}:${initialTime.getMinutes()}`)

			// 立即应用配置
			setTimeout(() => {
				this.updateMapboxMapStyle(initialConfig.mapbox)
			}, 1000) // 延迟1秒确保地图完全加载

			console.log("🌌 天空层和FOG效果已添加 (Standard样式)")
			console.log("🌞 太阳初始位置: 南方45°")
			console.log("🔍 相机限制已移除，可以向上看天空寻找太阳")
		} catch (error) {
			console.warn("添加天空层失败:", error)
			console.error("错误详情:", error)
		}
	}

	/**
	 * 设置天空层可见性
	 * @param visible 是否可见
	 */
	public setSkyVisible(visible: boolean): void {
		try {
			if (this.map.getLayer("sky")) {
				this.map.setPaintProperty("sky", "sky-opacity", visible ? 1 : 0)
			}
		} catch (error) {
			console.warn("设置天空层可见性失败:", error)
		}
	}

	/**
	 * 设置天空层颜色
	 * @param horizonColor 地平线颜色
	 * @param zenithColor 天顶颜色
	 */
	public setSkyColors(horizonColor: string, zenithColor: string): void {
		try {
			if (this.map.getLayer("sky")) {
				this.map.setPaintProperty("sky", "sky-gradient", [
					"interpolate",
					["linear"],
					["sky-radial-progress"],
					0.8, horizonColor,
					1, zenithColor
				])
			}
		} catch (error) {
			console.warn("设置天空层颜色失败:", error)
		}
	}

	/**
	 * 移除天空层
	 */
	public removeSkyLayer(): void {
		try {
			if (this.map.getLayer("sky")) {
				this.map.removeLayer("sky")
				console.log("天空层已移除")
			}
		} catch (error) {
			console.warn("移除天空层失败:", error)
		}
	}

	/**
	 * 更新太阳位置
	 * 这是低频更新，只在环境显著变化时调用
	 */
	/**
	 * 🚀 设置Three.js光照系统（公共接口）
	 * @param sunPosition 太阳位置和光照强度信息
	 */
	public setLight(sunPosition: SunPosition): void {
		// 只更新Three.js光照，避免重复调用Mapbox更新
		this.updateThreeJsLighting(sunPosition)

		// 光照更新时进行完整重绘
		this.repaint(true)
	}

	/**
	 * 更新Three.js光照系统
	 * @param sunPosition 太阳位置和光照强度信息
	 */
	private updateThreeJsLighting(sunPosition: SunPosition): void {
		try {
			if (!this.scene) return

			// 更新方向光（太阳光）
			const dirLight = this.scene.children.find(
				(child) => child instanceof THREE.DirectionalLight
			) as THREE.DirectionalLight
			
			if (dirLight) {
				// 设置光照方向
				dirLight.position.copy(sunPosition.direction.clone().multiplyScalar(1000))
				// 设置光照强度 - 增强模型光照以解决过暗问题
				dirLight.intensity = sunPosition.directionalIntensity * 3.5
				dirLight.updateMatrixWorld()
			}
			
			// 更新环境光
			const ambientLight = this.scene.children.find(
				(child) => child instanceof THREE.AmbientLight
			) as THREE.AmbientLight
			
			if (ambientLight) {
				// 设置环境光强度 - 增强环境光以提升模型整体亮度
				ambientLight.intensity = sunPosition.ambientIntensity * 2.8
			}
		} catch (error) {
			console.warn("更新Three.js光照失败:", error)
		}
	}

	/**
	 * 更新Mapbox地图明暗效果
	 * @param sunPosition 太阳位置和光照强度信息
	 */
	private updateMapboxLighting(sunPosition: SunPosition): void {
		try {
			// 计算地图亮度（基于环境光强度）- 降低地图亮度以避免过曝
			const mapBrightness = Math.max(0.15, Math.min(0.9, sunPosition.ambientIntensity * 1.5))
			this.updateMapBrightness(mapBrightness)
		} catch (error) {
			console.warn("更新Mapbox光照失败:", error)
		}
	}



	/**
	 * 🌞 更新太阳位置到Mapbox天空层
	 * 这是低频更新，只在环境显著变化时调用
	 */
	private updateSunPosition(sunPosition: SunPosition): void {
		try {
			// 🚀 立即同步更新：Mapbox天空层 + Three.js光照
			this.updateMapboxSkyLayer(sunPosition)
			this.updateThreeJsLighting(sunPosition)
			this.updateMapboxLighting(sunPosition)

			// 强制重绘确保同步
			this.repaint(true)

			console.log(`🌞 光照同步更新完成 - 高度角: ${(sunPosition.elevation * 180/Math.PI).toFixed(1)}°`)
		} catch (error) {
			console.warn("更新太阳位置失败:", error)
		}
	}

	/**
	 * 🌌 更新Mapbox天空层（太阳、月亮位置）
	 */
	private updateMapboxSkyLayer(sunPosition: SunPosition): void {
		try {
			if (this.map.getLayer("sky")) {
				// 转换为度数（Mapbox需要度数，不是弧度）
				let azimuthDegrees = sunPosition.azimuth * (180 / Math.PI)
				let elevationDegrees = sunPosition.elevation * (180 / Math.PI)

				// 🔧 修正坐标系转换
				// suncalc的方位角是从南方开始顺时针，Mapbox是从北方开始顺时针
				azimuthDegrees = (azimuthDegrees + 180) % 360

				// 🚨 确保方位角在0-360范围内（Mapbox要求）
				if (azimuthDegrees < 0) {
					azimuthDegrees += 360
				}
				azimuthDegrees = azimuthDegrees % 360

				// 确保高度角在合理范围内
				elevationDegrees = Math.max(-90, Math.min(90, elevationDegrees))

				console.log(`🔍 坐标转换检查 - 原始: ${(sunPosition.azimuth * 180/Math.PI).toFixed(1)}°, 转换后: ${azimuthDegrees.toFixed(1)}°`)

				// 🌞 设置太阳位置 [方位角, 高度角]
				this.map.setPaintProperty("sky", "sky-atmosphere-sun", [azimuthDegrees, elevationDegrees])

				// 根据太阳高度调整太阳强度（增加基础强度让太阳更明显）
				const sunIntensity = Math.max(10, 30 * Math.max(0, Math.sin(sunPosition.elevation)))
				this.map.setPaintProperty("sky", "sky-atmosphere-sun-intensity", sunIntensity)

				console.log(`🌞 太阳位置已更新 - 方位角: ${azimuthDegrees.toFixed(1)}° (Mapbox坐标), 高度角: ${elevationDegrees.toFixed(1)}°, 强度: ${sunIntensity.toFixed(1)}`)
			} else {
				console.warn("⚠️ 天空层不存在，无法更新太阳位置")
			}
		} catch (error) {
			console.warn("更新Mapbox天空层失败:", error)
			console.error("错误详情:", error)
		}
	}

	/**
	 * 🌌 更新天空颜色和大气效果 - 增强昼夜变化
	 * 这是低频更新，只在环境显著变化时调用
	 */
	private updateSkyColors(skyColors: SkyColors): void {
		try {
			// 🌫️ 增强的FOG效果 - 更明显的昼夜变化
			const starIntensity = skyColors.starIntensity || 0
			const isNight = starIntensity > 0.3
			const isDusk = starIntensity > 0.1 && starIntensity <= 0.3

			// 根据时间段调整FOG参数
			if (isNight) {
				// 🌙 夜晚配置 - 深色天空，明显星星
				this.map.setFog({
					"color": "rgba(25, 25, 50, 0.9)", // 深蓝色雾气
					"horizon-blend": 0.3, // 增强地平线混合
					"range": [0.5, 8] as [number, number], // 较近的雾气范围
					"high-color": "rgba(15, 15, 40, 1)", // 深色高空
					"space-color": "rgba(5, 5, 15, 1)", // 深黑太空色
					"star-intensity": 1 // 增强星星亮度
				})
			} else if (isDusk) {
				// 🌅 黄昏配置 - 暖色调过渡
				this.map.setFog({
					"color": skyColors.fogColor || "rgba(255, 150, 100, 0.8)", // 暖橙色雾气
					"horizon-blend": 0.25,
					"range": [0.8, 10] as [number, number],
					"high-color": "rgba(100, 50, 150, 1)", // 紫色高空
					"space-color": "rgba(30, 20, 60, 1)", // 深紫太空色
					"star-intensity": starIntensity * 0.8
				})
			} else {
				// ☀️ 白天配置 - 明亮清晰
				this.map.setFog({
					"color": skyColors.fogColor || "rgba(186, 210, 235, 0.6)", // 浅蓝雾气
					"horizon-blend": 0.15,
					"range": [1.2, 15] as [number, number], // 较远的雾气范围
					"high-color": skyColors.zenithColor || "rgba(135, 206, 250, 1)", // 明亮天蓝
					"space-color": "rgba(100, 150, 255, 1)", // 明亮天空色
					"star-intensity": 0 // 白天无星星
				})
			}

			console.log(`🌌 天空效果已更新 - 时段: ${isNight ? '夜晚🌙' : isDusk ? '黄昏🌅' : '白天☀️'}, 星星强度: ${starIntensity.toFixed(2)}`)
		} catch (error) {
			console.warn("更新天空颜色失败:", error)
		}
	}

	/**
	 * 🎨 应用完整的光照配置系统
	 * 统一控制天空盒、Mapbox地图、Three.js光照
	 */
	private applyCompleteLightingConfig(config: CompleteLightingConfig, sunPosition: SunPosition): void {
		try {
			console.log('🎨 应用完整光照配置...')

			// 1. 🌌 更新Mapbox天空层
			this.updateMapboxSkyWithConfig(config.sky, sunPosition)

			// 2. 🗺️ 更新Mapbox地图样式
			this.updateMapboxMapStyle(config.mapbox)

			// 3. 💡 更新Three.js光照
			this.updateThreeJsLightingWithConfig(config.threejs)

			console.log('✅ 完整光照配置应用完成')
		} catch (error) {
			console.warn("应用完整光照配置失败:", error)
		}
	}

	/**
	 * 🌌 使用配置更新Mapbox天空层
	 */
	private updateMapboxSkyWithConfig(skyConfig: CompleteLightingConfig['sky'], sunPosition: SunPosition): void {
		try {
			// 更新太阳位置和强度
			if (this.map.getLayer("sky")) {
				// 转换坐标
				let azimuthDegrees = sunPosition.azimuth * (180 / Math.PI)
				let elevationDegrees = sunPosition.elevation * (180 / Math.PI)
				azimuthDegrees = (azimuthDegrees + 180) % 360
				if (azimuthDegrees < 0) azimuthDegrees += 360
				elevationDegrees = Math.max(-90, Math.min(90, elevationDegrees))

				// 设置太阳位置和强度
				this.map.setPaintProperty("sky", "sky-atmosphere-sun", [azimuthDegrees, elevationDegrees])
				this.map.setPaintProperty("sky", "sky-atmosphere-sun-intensity", skyConfig.sunIntensity)
			}

			// 更新FOG效果
			this.map.setFog({
				"color": skyConfig.fogColor,
				"horizon-blend": skyConfig.horizonBlend,
				"range": skyConfig.fogRange,
				"high-color": skyConfig.highColor,
				"space-color": skyConfig.spaceColor,
				"star-intensity": skyConfig.starIntensity
			})

			console.log(`🌌 天空层已更新 - 星星强度: ${skyConfig.starIntensity}`)
			console.log(`⭐ FOG配置详情:`, {
				color: skyConfig.fogColor,
				horizonBlend: skyConfig.horizonBlend,
				range: skyConfig.fogRange,
				highColor: skyConfig.highColor,
				spaceColor: skyConfig.spaceColor,
				starIntensity: skyConfig.starIntensity
			})
		} catch (error) {
			console.warn("更新Mapbox天空层失败:", error)
		}
	}

	/**
	 * 🗺️ 更新Mapbox地图样式
	 */
	private updateMapboxMapStyle(mapboxConfig: CompleteLightingConfig['mapbox']): void {
		try {
			// 创建颜色遮罩层
			if (!this.map.getLayer('lighting-overlay')) {
				this.map.addLayer({
					id: 'lighting-overlay',
					type: 'background',
					paint: {
						'background-color': mapboxConfig.overlayColor,
						'background-opacity': mapboxConfig.overlayOpacity
					}
				})
			} else {
				// 更新现有遮罩层
				this.map.setPaintProperty('lighting-overlay', 'background-color', mapboxConfig.overlayColor)
				this.map.setPaintProperty('lighting-overlay', 'background-opacity', mapboxConfig.overlayOpacity)
			}

			console.log(`🗺️ 地图样式已更新 - 亮度: ${mapboxConfig.brightness}, 遮罩: ${mapboxConfig.overlayColor}`)
		} catch (error) {
			console.warn("更新Mapbox地图样式失败:", error)
		}
	}

	/**
	 * 💡 使用配置更新Three.js光照
	 */
	private updateThreeJsLightingWithConfig(lightingConfig: CompleteLightingConfig['threejs']): void {
		try {
			if (!this.scene) return

			// 查找或创建直射光
			let directionalLight = this.scene.children.find(
				child => child instanceof THREE.DirectionalLight
			) as THREE.DirectionalLight

			if (!directionalLight) {
				directionalLight = new THREE.DirectionalLight()
				directionalLight.castShadow = true
				this.scene.add(directionalLight)
			}

			// 更新直射光
			directionalLight.color.setStyle(lightingConfig.directionalLight.color)
			directionalLight.intensity = lightingConfig.directionalLight.intensity
			directionalLight.position.copy(lightingConfig.directionalLight.position)

			// 查找或创建环境光
			let ambientLight = this.scene.children.find(
				child => child instanceof THREE.AmbientLight
			) as THREE.AmbientLight

			if (!ambientLight) {
				ambientLight = new THREE.AmbientLight()
				this.scene.add(ambientLight)
			}

			// 更新环境光
			ambientLight.color.setStyle(lightingConfig.ambientLight.color)
			ambientLight.intensity = lightingConfig.ambientLight.intensity

			// 查找或创建半球光
			let hemisphereLight = this.scene.children.find(
				child => child instanceof THREE.HemisphereLight
			) as THREE.HemisphereLight

			if (!hemisphereLight) {
				hemisphereLight = new THREE.HemisphereLight()
				this.scene.add(hemisphereLight)
			}

			// 更新半球光
			hemisphereLight.color.setStyle(lightingConfig.hemisphereLight.skyColor)
			hemisphereLight.groundColor.setStyle(lightingConfig.hemisphereLight.groundColor)
			hemisphereLight.intensity = lightingConfig.hemisphereLight.intensity

			console.log(`💡 Three.js光照已更新 - 直射光: ${lightingConfig.directionalLight.intensity}, 环境光: ${lightingConfig.ambientLight.intensity}`)
		} catch (error) {
			console.warn("更新Three.js光照失败:", error)
		}
	}

	/**
	 * 🌅 更新地图亮度 - 使用更好的光照控制方案
	 * 不再使用透明度，而是使用Mapbox的光照和FOG系统
	 */
	private updateMapBrightness(brightness: number): void {
		try {
			// 🌫️ 使用FOG系统控制地图整体亮度和氛围
			const currentFog = this.map.getFog()
			if (currentFog) {
				// 根据亮度调整雾的颜色和强度
				const fogIntensity = Math.max(0.1, Math.min(1.0, brightness))
				const fogColor = brightness > 0.5 ?
					`rgba(186, 210, 235, ${fogIntensity * 0.8})` :  // 白天：浅蓝色
					`rgba(25, 25, 50, ${fogIntensity * 0.9})`       // 夜晚：深蓝色

				this.map.setFog({
					...currentFog,
					"color": fogColor,
					// 根据亮度调整雾的范围
					"range": brightness > 0.5 ? [0.8, 8] : [0.3, 5],
					// 调整地平线混合强度
					"horizon-blend": brightness > 0.5 ? 0.1 : 0.2
				})
			}

			// 🎨 使用地图样式的光照属性（如果支持）
			if (this.map.isStyleLoaded()) {
				try {
					// 尝试设置地图的整体光照强度
					const lightIntensity = Math.max(0.3, Math.min(1.2, brightness * 1.2))

					// 如果使用的是支持光照的样式，设置光照强度
					if (this.map.getStyle().name?.includes('standard')) {
						this.map.setConfigProperty('basemap', 'lightIntensity', lightIntensity)
					}
				} catch (lightError) {
					// 如果样式不支持光照属性，忽略错误
					console.log('当前样式不支持光照属性')
				}
			}

			console.log(`🌅 地图亮度已更新 - 亮度: ${brightness.toFixed(2)}`)
		} catch (error) {
			console.warn("更新地图亮度失败:", error)
		}
	}



	/**
	 * 获取时间系统实例
	 */
	public getTimeSystem(): TimeSystem | undefined {
		return this.timeSystem
	}

	/**
	 * 设置当前时间
	 */
	public setCurrentTime(time: Date): void {
		if (this.timeSystem) {
			this.timeSystem.setCurrentTime(time)
		}
	}

	/**
	 * 获取当前时间
	 */
	public getCurrentTime(): Date | undefined {
		return this.timeSystem?.getCurrentTime()
	}

	/**
	 * 启动时间系统
	 */
	public startTimeSystem(): void {
		if (this.timeSystem) {
			this.timeSystem.start()
		}
	}

	/**
	 * 停止时间系统
	 */
	public stopTimeSystem(): void {
		if (this.timeSystem) {
			this.timeSystem.stop()
		}
	}

	/**
	 * 设置时间加速倍数
	 */
	public setTimeScale(scale: number): void {
		if (this.timeSystem) {
			this.timeSystem.setTimeScale(scale)
		}
	}

	/**
	 * 获取时间加速倍数
	 */
	public getTimeScale(): number {
		return this.timeSystem?.getTimeScale() || 1
	}
}
