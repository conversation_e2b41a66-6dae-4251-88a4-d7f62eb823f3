import * as THREE from "three"
import { Water } from "three/examples/jsm/objects/Water.js"
import { eventBus } from "./EventBus"
import { FBXLoader } from "three/examples/jsm/Addons.js"
import { RGBELoader } from "three/examples/jsm/loaders/RGBELoader.js"

/**
 * 水面效果类
 * 参照 Three.js ocean 示例实现
 */
export class WaterSurface {
	private scene: THREE.Scene
	private water: Water | null = null
	private waterGeometry: THREE.PlaneGeometry | null = null
	private clock: THREE.Clock = new THREE.Clock()

	// 水底贴图相关
	private underwaterPlane: THREE.Mesh | null = null
	private underwaterGeometry: THREE.PlaneGeometry | null = null
	private underwaterMaterial: THREE.MeshBasicMaterial | null = null

	// 鱼类动画相关
	private fishModels: THREE.Group[] = []
	private fishMixers: THREE.AnimationMixer[] = []
	private fishAnimations: THREE.AnimationAction[] = []

	// 鱼类加载优化
	private fishModelCache: THREE.Group | null = null // 缓存原始模型
	private fishAnimationClips: THREE.AnimationClip[] = [] // 缓存动画片段
	private fishCount: number = 20 // 默认鱼类数量
	private fishSpawnRadius: number = 8.5 // 鱼类生成半径

	// 鱼类移动相关
	private fishMovementData: Array<{
		velocity: THREE.Vector3
		targetPosition: THREE.Vector3
		changeDirectionTime: number
		speed: number
	}> = []
	private fishMovementEnabled: boolean = true
	private fishSpeed: number = 0.5 // 基础移动速度
	private directionChangeInterval: number = 3000 // 改变方向的间隔（毫秒）
	private fishRotationSpeed: number = 0.02 // 鱼类旋转速度
	private fishOrientationOffset: number = -Math.PI / 2 // 鱼类朝向偏移（先试试0度）

	// 天空盒相关
	private skybox: THREE.Mesh | null = null
	private environmentMap: THREE.Texture | null = null

	// 水面参数
	private waterSize = 50
	private waterSegments = 128

	// 顶点坐标（用户提供的四个顶点）
	private vertices = [
		new THREE.Vector3(-10, -7.3, -32),
		new THREE.Vector3(-10, -7.3, -10),
		new THREE.Vector3(-32, -7.3, -10),
		new THREE.Vector3(-32, -7.3, -32),
	]

	constructor(scene: THREE.Scene) {
		this.scene = scene
		
		// 先加载天空盒，再创建水面（这样水面可以使用环境贴图）
		this.loadSkybox().then(() => {
			this.createWaterSurface()
		})
		
		eventBus.on("water-visibility-change", (eventData) => {
			const { visible } = eventData
			this.setVisible(visible)
		})
	}

	/**
	 * 加载天空盒和环境贴图
	 */
	private async loadSkybox(): Promise<void> {
		try {
			const rgbeLoader = new RGBELoader()
			
			// 加载HDR环境贴图
			this.environmentMap = await new Promise<THREE.Texture>((resolve, reject) => {
				rgbeLoader.load(
					'texture/citrus_orchard_road_puresky_1k.hdr',

					(texture) => {
						texture.mapping = THREE.EquirectangularReflectionMapping
						resolve(texture)
					},
					undefined,
					(error) => reject(error)
				)
			})

			// 设置场景环境贴图（用于反射）
			this.scene.environment = this.environmentMap

			// 在Mapbox+Three.js叠加模式下，不创建完整天空盒以避免遮挡地图
			// 只使用环境贴图提供反射效果
			// 如果需要天空盒效果，可以考虑创建局部天空盒或使用Mapbox的天空层

			console.log('🌅 天空盒加载成功')
		} catch (error) {
			console.error('❌ 天空盒加载失败:', error)
		}
	}

	/**
	 * 创建水面
	 */
	private createWaterSurface(): void {
		// 计算水面的中心点和尺寸
		const bounds = this.calculateBounds()

		// 创建水面几何体
		this.waterGeometry = new THREE.PlaneGeometry(
			bounds.width,
			bounds.height,
			this.waterSegments,
			this.waterSegments
		)

		// 使用Three.js Water类创建水面
		this.water = new Water(this.waterGeometry, {
			textureWidth: 1024, // 提升反射纹理分辨率
			textureHeight: 1024,
			waterNormals: new THREE.TextureLoader().load(
				"texture/water_normal_2.jpeg",
				function (texture) {
					texture.wrapS = texture.wrapT = THREE.RepeatWrapping
				}
			),
			sunDirection: new THREE.Vector3(0.70707, 0.70707, 0), // 45度角的太阳光
			sunColor: 0xFFFFFF,
			waterColor: 0x008800,
			distortionScale: 10,
			fog: this.scene.fog !== undefined,
			alpha: 0.85,
		})

		// 环境贴图已通过scene.environment设置，Water类会自动使用场景环境

		// 设置水面透明度为50%
		this.water.material.transparent = true
		this.water.material.opacity = 0.5

		// 设置水面位置和旋转
		this.water.position.set(bounds.centerX, bounds.centerY, bounds.centerZ)
		this.water.rotation.x = -Math.PI / 2 // 水平放置

		// 添加到场景
		this.scene.add(this.water)
		
		// 创建水底贴图
		this.createUnderwaterPlane(bounds)
		
		this.addFish()

		console.log("🌊 水面已添加到场景", {
			position: this.water.position,
			size: { width: bounds.width, height: bounds.height },
		})
	}

	/**
	 * 创建水底贴图平面
	 */
	private createUnderwaterPlane(bounds: any): void {
		// 创建水底几何体，使用与水面相同的尺寸
		this.underwaterGeometry = new THREE.PlaneGeometry(
			bounds.width,
			bounds.height
		)

		// 加载水底贴图
		const textureLoader = new THREE.TextureLoader()
		const underwaterTexture = textureLoader.load("texture/underwater.jpg")

		
		// 设置贴图重复模式
		underwaterTexture.wrapS = THREE.RepeatWrapping
		underwaterTexture.wrapT = THREE.RepeatWrapping
		// 可以调整重复次数来控制贴图密度
		underwaterTexture.repeat.set(1, 1)

		// 创建水底材质
		this.underwaterMaterial = new THREE.MeshBasicMaterial({
			map: underwaterTexture,
			transparent: true,
			opacity: 0.8,
			side: THREE.DoubleSide // 双面材质，确保从各个角度都能看到
		})

		// 创建水底平面网格
		this.underwaterPlane = new THREE.Mesh(
			this.underwaterGeometry,
			this.underwaterMaterial
		)

		// 设置水底平面位置（在水面下5米）
		this.underwaterPlane.position.set(
			bounds.centerX,
			bounds.centerY - 2, // 水面下5米
			bounds.centerZ
		)
		
		// 水平放置
		this.underwaterPlane.rotation.x = -Math.PI / 2

		// 添加到场景
		this.scene.add(this.underwaterPlane)

		console.log("🏞️ 水底贴图已添加到场景", {
			position: this.underwaterPlane.position,
			size: { width: bounds.width, height: bounds.height },
		})
	}

	/**
	 * 加载鱼类模型（只加载一次，用于克隆）
	 */
	private async loadFishModel(): Promise<void> {
		if (this.fishModelCache) {
			console.log("🐟 使用缓存的鱼类模型")
			return
		}

		try {
			console.log("🐟 开始加载鱼类模型...")

			const fbxLoader = new FBXLoader()
			const fishModel = await fbxLoader.loadAsync("/models/koifish.fbx")

			console.log("🐟 鱼类模型加载成功:", fishModel)
			console.log("🐟 模型动画数量:", fishModel.animations.length)

			// 缓存原始模型和动画
			this.fishModelCache = fishModel
			this.fishAnimationClips = [...fishModel.animations]

			if (fishModel.animations.length > 0) {
				console.log("🐟 可用动画:")
				fishModel.animations.forEach((clip, index) => {
					console.log(
						`  ${index}: ${clip.name} (时长: ${clip.duration}s)`
					)
				})
			} else {
				console.warn("🐟 模型中没有找到动画")
			}

			console.log("🐟 鱼类模型缓存完成")
		} catch (error) {
			console.error("🐟 加载鱼类模型失败:", error)
			throw error
		}
	}

	/**
	 * 创建单条鱼实例
	 */
	private createFishInstance(index: number): void {
		if (!this.fishModelCache) {
			console.warn("🐟 鱼类模型未缓存，无法创建实例")
			return
		}

		// 克隆模型
		const fishInstance = this.fishModelCache.clone()

		// 设置基础属性
		fishInstance.scale.setScalar(0.002)

		// 计算随机位置（在水面区域内）
		const waterBounds = this.calculateBounds()
		const angle =
			(index / this.fishCount) * Math.PI * 2 + Math.random() * 0.5
		const radius = Math.random() * this.fishSpawnRadius

		const x = waterBounds.centerX + Math.cos(angle) * radius
		const z = waterBounds.centerZ + Math.sin(angle) * radius
		const y = waterBounds.centerY - 0.5 + Math.random() * 1 // 稍微在水面上方

		fishInstance.position.set(x, y, z)

		// 设置初始朝向（使用可配置的偏移）
		fishInstance.rotation.y = angle + this.fishOrientationOffset

		// 创建动画混合器
		const mixer = new THREE.AnimationMixer(fishInstance)

		// 设置动画
		if (this.fishAnimationClips.length > 0) {
			const swimAction = mixer.clipAction(this.fishAnimationClips[0])
			swimAction.setLoop(THREE.LoopRepeat, Infinity)

			// 添加随机的动画偏移，让鱼类动画不同步
			const randomOffset =
				Math.random() * this.fishAnimationClips[0].duration
			swimAction.time = randomOffset

			swimAction.play()
			this.fishAnimations.push(swimAction)
		}

		// 添加到场景
		this.scene.add(fishInstance)

		// 初始化移动数据
		const movementData = {
			velocity: new THREE.Vector3(
				(Math.random() - 0.5) * this.fishSpeed,
				0,
				(Math.random() - 0.5) * this.fishSpeed
			),
			targetPosition: this.generateRandomTargetPosition(),
			changeDirectionTime:
				Date.now() + Math.random() * this.directionChangeInterval,
			speed: this.fishSpeed * (0.8 + Math.random() * 0.4), // 每条鱼的速度略有不同
		}

		// 保存引用
		this.fishModels.push(fishInstance)
		this.fishMixers.push(mixer)
		this.fishMovementData.push(movementData)

		// console.log(`🐟 创建鱼类实例 ${index + 1}/${this.fishCount}`)
	}

	/**
	 * 添加多条鱼类模型和骨骼动画
	 */
	private async addFish(): Promise<void> {
		try {
			// 先加载模型到缓存
			await this.loadFishModel()

			// 创建多个鱼类实例
			console.log(`🐟 开始创建 ${this.fishCount} 条鱼...`)

			for (let i = 0; i < this.fishCount; i++) {
				this.createFishInstance(i)
			}

			console.log(`🐟 成功创建 ${this.fishCount} 条鱼类`)
		} catch (error) {
			console.error("🐟 添加鱼类失败:", error)
		}
	}

	/**
	 * 生成水面范围内的随机目标位置
	 */
	private generateRandomTargetPosition(): THREE.Vector3 {
		const bounds = this.calculateBounds()

		// 在水面边界内生成随机位置，留一些边距
		const margin = 1
		const x =
			bounds.centerX + (Math.random() - 0.5) * (bounds.width - margin * 2)
		const z =
			bounds.centerZ +
			(Math.random() - 0.5) * (bounds.height - margin * 2)
		const y = bounds.centerY - 2 + Math.random() * 1 // 与鱼类初始位置保持一致

		return new THREE.Vector3(x, y, z)
	}

	/**
	 * 检查位置是否在水面范围内
	 */
	private isPositionInWaterBounds(position: THREE.Vector3): boolean {
		const bounds = this.calculateBounds()
		const margin = 1

		return (
			position.x >= bounds.centerX - bounds.width / 2 + margin &&
			position.x <= bounds.centerX + bounds.width / 2 - margin &&
			position.z >= bounds.centerZ - bounds.height / 2 + margin &&
			position.z <= bounds.centerZ + bounds.height / 2 - margin
		)
	}

	/**
	 * 计算边界框
	 */
	private calculateBounds() {
		const minX = Math.min(...this.vertices.map((v) => v.x))
		const maxX = Math.max(...this.vertices.map((v) => v.x))
		const minY = Math.min(...this.vertices.map((v) => v.y))
		const maxY = Math.max(...this.vertices.map((v) => v.y))
		const minZ = Math.min(...this.vertices.map((v) => v.z))
		const maxZ = Math.max(...this.vertices.map((v) => v.z))

		return {
			centerX: (minX + maxX) / 2,
			centerY: (minY + maxY) / 2,
			centerZ: (minZ + maxZ) / 2,
			width: maxX - minX,
			height: maxZ - minZ,
		}
	}

	/**
	 * 更新水面动画和鱼类骨骼动画
	 */
	public update(camera?: THREE.Camera): void {
		const deltaTime = this.clock.getDelta()

		// 更新水面动画
		if (this.water) {
			const time = this.clock.getElapsedTime()
			// Water类会自动处理时间更新
			this.water.material.uniforms["time"].value = time / 2

			// 修复Mapbox GL中的相机反射问题
			if (camera) {
				this.updateWaterReflection(camera)
			}
		}

		// 更新鱼类骨骼动画
		this.fishMixers.forEach((mixer) => {
			mixer.update(deltaTime)
		})

		// 更新鱼类移动
		if (this.fishMovementEnabled) {
			this.updateFishMovement(deltaTime)
		}
	}

	/**
	 * 更新鱼类移动
	 */
	private updateFishMovement(deltaTime: number): void {
		const currentTime = Date.now()

		this.fishModels.forEach((fishModel, index) => {
			const movementData = this.fishMovementData[index]
			if (!movementData) return

			// 检查是否需要改变方向
			if (currentTime > movementData.changeDirectionTime) {
				// 生成新的目标位置
				movementData.targetPosition =
					this.generateRandomTargetPosition()
				movementData.changeDirectionTime =
					currentTime + this.directionChangeInterval

				// 计算朝向目标的方向
				const direction = movementData.targetPosition
					.clone()
					.sub(fishModel.position)
					.normalize()
				movementData.velocity
					.copy(direction)
					.multiplyScalar(movementData.speed)
			}

			// 计算新位置
			const newPosition = fishModel.position
				.clone()
				.add(movementData.velocity.clone().multiplyScalar(deltaTime))

			// 检查是否到达目标或超出边界
			const distanceToTarget = fishModel.position.distanceTo(
				movementData.targetPosition
			)
			if (
				distanceToTarget < 1 ||
				!this.isPositionInWaterBounds(newPosition)
			) {
				// 生成新目标
				movementData.targetPosition =
					this.generateRandomTargetPosition()
				const direction = movementData.targetPosition
					.clone()
					.sub(fishModel.position)
					.normalize()
				movementData.velocity
					.copy(direction)
					.multiplyScalar(movementData.speed)
			} else {
				// 更新位置
				fishModel.position.copy(newPosition)
			}

			// 让鱼朝向移动方向
			if (movementData.velocity.length() > 0.01) {
				const lookDirection = movementData.velocity.clone().normalize()

				// 计算鱼应该面向的角度（在XZ平面上）
				// 使用可配置的朝向偏移
				const targetAngle =
					Math.atan2(lookDirection.x, lookDirection.z) +
					this.fishOrientationOffset

				// 平滑旋转到目标角度
				let currentAngle = fishModel.rotation.y
				let angleDiff = targetAngle - currentAngle

				// 处理角度差值，确保选择最短路径
				if (angleDiff > Math.PI) {
					angleDiff -= 2 * Math.PI
				} else if (angleDiff < -Math.PI) {
					angleDiff += 2 * Math.PI
				}

				// 平滑旋转（使用可配置的旋转速度）
				fishModel.rotation.y += angleDiff * this.fishRotationSpeed
			}
		})
	}

	/**
	 * 修复Mapbox GL中的水面反射
	 */
	private updateWaterReflection(camera: THREE.Camera): void { }

	/**
	 * 设置水面参数
	 */
	public setWaterParams(params: {
		distortionScale?: number
		waterColor?: number
		sunColor?: number
		sunDirection?: THREE.Vector3
		opacity?: number
	}): void {
		if (!this.water) return

		const material = this.water.material as any

		if (params.distortionScale !== undefined) {
			material.uniforms["distortionScale"].value = params.distortionScale
		}
		if (params.waterColor !== undefined) {
			material.uniforms["waterColor"].value = new THREE.Color(
				params.waterColor
			)
		}
		if (params.sunColor !== undefined) {
			material.uniforms["sunColor"].value = new THREE.Color(
				params.sunColor
			)
		}
		if (params.sunDirection) {
			material.uniforms["sunDirection"].value = params.sunDirection
		}
		if (params.opacity !== undefined) {
			material.transparent = true
			material.opacity = params.opacity
		}
	}

	/**
	 * 设置水面透明度
	 */
	public setOpacity(opacity: number): void {
		if (this.water) {
			this.water.material.transparent = true
			this.water.material.opacity = Math.max(0, Math.min(1, opacity))
		}
	}

	/**
	 * 设置噪波强度（扭曲程度）
	 */
	public setDistortionScale(scale: number): void {
		if (this.water) {
			const material = this.water.material as any
			if (material.uniforms && material.uniforms["distortionScale"]) {
				material.uniforms["distortionScale"].value = scale
			}
		}
	}

	/**
	 * 关闭噪波
	 */
	public disableDistortion(): void {
		this.setDistortionScale(0.0)
	}

	/**
	 * 启用噪波
	 */
	public enableDistortion(scale: number = 3.7): void {
		this.setDistortionScale(scale)
	}

	/**
	 * 设置太阳方向（影响反射效果）
	 */
	public setSunDirection(direction: THREE.Vector3): void {
		if (this.water) {
			const material = this.water.material as any
			if (material.uniforms && material.uniforms.sunDirection) {
				material.uniforms.sunDirection.value.copy(direction.normalize())
			}
		}
	}

	/**
	 * 根据时间自动更新太阳方向
	 */
	public updateSunDirection(timeOfDay: number = 0.5): void {
		// timeOfDay: 0 = 日出, 0.5 = 正午, 1 = 日落
		const angle = timeOfDay * Math.PI
		const sunDirection = new THREE.Vector3(
			Math.cos(angle),
			Math.sin(angle) * 0.8 + 0.2, // 保持一定高度
			0.3
		)
		this.setSunDirection(sunDirection)
	}

	/**
	 * 控制鱼类动画播放/暂停
	 */
	public setFishAnimationEnabled(enabled: boolean): void {
		this.fishAnimations.forEach((action) => {
			if (enabled) {
				action.play()
			} else {
				action.stop()
			}
		})
		console.log(`🐟 鱼类动画${enabled ? "播放" : "暂停"}`)
	}

	/**
	 * 设置鱼类动画速度
	 */
	public setFishAnimationSpeed(speed: number): void {
		this.fishAnimations.forEach((action) => {
			action.setEffectiveTimeScale(speed)
		})
		console.log(`🐟 鱼类动画速度设置为: ${speed}x`)
	}

	/**
	 * 启用/禁用鱼类移动
	 */
	public setFishMovementEnabled(enabled: boolean): void {
		this.fishMovementEnabled = enabled
		console.log(`🐟 鱼类移动${enabled ? "启用" : "禁用"}`)
	}

	/**
	 * 设置鱼类移动速度
	 */
	public setFishMovementSpeed(speed: number): void {
		this.fishSpeed = Math.max(0, speed)

		// 更新所有鱼类的移动速度
		this.fishMovementData.forEach((data) => {
			data.speed = this.fishSpeed * (0.8 + Math.random() * 0.4)
		})

		console.log(`🐟 鱼类移动速度设置为: ${speed}`)
	}

	/**
	 * 设置鱼类改变方向的间隔
	 */
	public setFishDirectionChangeInterval(intervalMs: number): void {
		this.directionChangeInterval = Math.max(1000, intervalMs)
		console.log(`🐟 鱼类方向改变间隔设置为: ${intervalMs}ms`)
	}

	/**
	 * 设置鱼类旋转速度
	 */
	public setFishRotationSpeed(speed: number): void {
		this.fishRotationSpeed = Math.max(0.001, Math.min(1.0, speed))
		console.log(`🐟 鱼类旋转速度设置为: ${this.fishRotationSpeed}`)
	}

	/**
	 * 设置鱼类朝向偏移（用于修正模型默认朝向）
	 */
	public setFishOrientationOffset(offsetRadians: number): void {
		const oldOffset = this.fishOrientationOffset
		this.fishOrientationOffset = offsetRadians

		// 调整所有现有鱼类的朝向
		const offsetDiff = offsetRadians - oldOffset
		this.fishModels.forEach((fishModel) => {
			fishModel.rotation.y += offsetDiff
		})

		console.log(
			`🐟 鱼类朝向偏移设置为: ${offsetRadians} 弧度 (${(offsetRadians * 180) / Math.PI
			}度)`
		)
	}

	/**
	 * 获取鱼类调试信息
	 */
	public getFishDebugInfo(): any {
		return {
			fishCount: this.fishModels.length,
			movementEnabled: this.fishMovementEnabled,
			fishSpeed: this.fishSpeed,
			directionChangeInterval: this.directionChangeInterval,
			fishPositions: this.fishModels.map((fish, index) => ({
				index,
				position: fish.position.clone(),
				rotation: fish.rotation.clone(),
				velocity: this.fishMovementData[index]?.velocity.clone(),
				targetPosition:
					this.fishMovementData[index]?.targetPosition.clone(),
			})),
		}
	}

	/**
	 * 获取鱼类模型数量
	 */
	public getFishCount(): number {
		return this.fishModels.length
	}

	/**
	 * 设置鱼类数量并重新生成
	 */
	public async setFishCount(count: number): Promise<void> {
		if (count < 0) {
			console.warn("🐟 鱼类数量不能为负数")
			return
		}

		this.fishCount = count

		// 移除现有的鱼类
		this.removeAllFish()

		// 重新生成鱼类
		if (count > 0) {
			await this.addFish()
		}

		console.log(`🐟 鱼类数量已设置为: ${count}`)
	}

	/**
	 * 设置鱼类生成半径
	 */
	public setFishSpawnRadius(radius: number): void {
		this.fishSpawnRadius = Math.max(0, radius)
		console.log(`🐟 鱼类生成半径设置为: ${radius}`)
	}

	/**
	 * 添加更多鱼类实例
	 */
	public async addMoreFish(additionalCount: number): Promise<void> {
		if (additionalCount <= 0) return

		try {
			// 确保模型已缓存
			await this.loadFishModel()

			const startIndex = this.fishModels.length

			console.log(`🐟 添加 ${additionalCount} 条额外的鱼...`)

			for (let i = 0; i < additionalCount; i++) {
				this.createFishInstance(startIndex + i)
			}

			console.log(
				`🐟 成功添加 ${additionalCount} 条鱼，总数: ${this.fishModels.length}`
			)
		} catch (error) {
			console.error("🐟 添加额外鱼类失败:", error)
		}
	}

	/**
	 * 移除所有鱼类模型
	 */
	public removeAllFish(): void {
		this.fishModels.forEach((fishModel) => {
			this.scene.remove(fishModel)
		})

		this.fishAnimations.forEach((action) => {
			action.stop()
		})

		this.fishMixers.forEach((mixer) => {
			mixer.stopAllAction()
		})

		this.fishModels.length = 0
		this.fishMixers.length = 0
		this.fishAnimations.length = 0
		this.fishMovementData.length = 0 // 清理移动数据

		console.log("🐟 所有鱼类模型已移除")
	}

	/**
	 * 显示/隐藏水面
	 */
	public setVisible(visible: boolean): void {
		if (this.water) {
			this.water.visible = visible
		}

		// 同时控制水底贴图的显示/隐藏
		if (this.underwaterPlane) {
			this.underwaterPlane.visible = visible
		}

		// 同时控制鱼类的显示/隐藏
		this.fishModels.forEach((fishModel) => {
			fishModel.visible = visible
		})
	}

	/**
	 * 销毁水面和鱼类资源
	 */
	public dispose(): void {
		// 清理鱼类资源
		this.removeAllFish()

		// 清理缓存的鱼类模型
		if (this.fishModelCache) {
			// 遍历并清理模型的几何体和材质
			this.fishModelCache.traverse((child) => {
				if (child instanceof THREE.Mesh) {
					if (child.geometry) {
						child.geometry.dispose()
					}
					if (child.material) {
						if (Array.isArray(child.material)) {
							child.material.forEach((material) =>
								material.dispose()
							)
						} else {
							child.material.dispose()
						}
					}
				}
			})
			this.fishModelCache = null
		}

		// 清理动画片段缓存
		this.fishAnimationClips.length = 0

		// 清理水底贴图资源
		if (this.underwaterPlane) {
			this.scene.remove(this.underwaterPlane)

			if (this.underwaterGeometry) {
				this.underwaterGeometry.dispose()
			}

			if (this.underwaterMaterial) {
				if (this.underwaterMaterial.map) {
					this.underwaterMaterial.map.dispose()
				}
				this.underwaterMaterial.dispose()
			}

			this.underwaterPlane = null
			this.underwaterGeometry = null
			this.underwaterMaterial = null
		}

		// 清理水面资源
		if (this.water) {
			this.scene.remove(this.water)

			if (this.waterGeometry) {
				this.waterGeometry.dispose()
			}

			// Water类会自动处理材质的销毁
			this.water = null
			this.waterGeometry = null
		}

		// 在Mapbox模式下，天空盒已禁用，无需清理天空盒网格

		// 清理环境贴图
		if (this.environmentMap) {
			this.environmentMap.dispose()
			this.environmentMap = null
		}

		// 清理场景环境贴图引用
		if (this.scene.environment) {
			this.scene.environment = null
		}

		console.log("🌊 水面、水底贴图、鱼类和天空盒资源已完全清理")
	}

	/**
	 * 设置水底贴图透明度
	 */
	public setUnderwaterOpacity(opacity: number): void {
		if (this.underwaterMaterial) {
			this.underwaterMaterial.opacity = Math.max(0, Math.min(1, opacity))
		}
	}

	/**
	 * 设置水底贴图深度（相对于水面的距离）
	 */
	public setUnderwaterDepth(depth: number): void {
		if (this.underwaterPlane && this.water) {
			this.underwaterPlane.position.y = this.water.position.y - Math.abs(depth)
		}
	}

	/**
	 * 设置水底贴图重复次数
	 */
	public setUnderwaterTextureRepeat(repeatX: number, repeatY: number): void {
		if (this.underwaterMaterial && this.underwaterMaterial.map) {
			this.underwaterMaterial.map.repeat.set(repeatX, repeatY)
			this.underwaterMaterial.map.needsUpdate = true
		}
	}

	/**
	 * 获取水底贴图网格
	 */
	public getUnderwaterMesh(): THREE.Mesh | null {
		return this.underwaterPlane
	}

	/**
	 * 获取水面网格
	 */
	public getWaterMesh(): Water | null {
		return this.water
	}

	/**
	 * 设置天空盒可见性
	 * 注意：在Mapbox模式下，天空盒已禁用以避免遮挡地图
	 */
	public setSkyboxVisible(visible: boolean): void {
		// 在Mapbox+Three.js叠加模式下，天空盒已禁用
		console.log(`🌅 天空盒在Mapbox模式下已禁用，环境反射仍然有效`)
	}

	/**
	 * 设置环境反射强度
	 * 注意：Water类使用ShaderMaterial，环境反射通过scene.environment自动处理
	 */
	public setEnvironmentIntensity(intensity: number): void {
		// Water类的ShaderMaterial不支持直接设置envMapIntensity
		// 环境反射效果通过scene.environment和Water的内置shader处理
		console.log(`🌊 环境反射强度设置请求: ${intensity} (Water类自动处理)`)
	}

	/**
	 * 获取天空盒网格
	 */
	public getSkybox(): THREE.Mesh | null {
		return this.skybox
	}

	/**
	 * 获取环境贴图
	 */
	public getEnvironmentMap(): THREE.Texture | null {
		return this.environmentMap
	}
}
