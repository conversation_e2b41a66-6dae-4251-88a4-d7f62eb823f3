import * as THREE from "three"
import { PathPointList, PathGeometry } from "@/lib/three.path.min"
import travelUrl from "@/assets/image/traveled.png"
import unTravelUrl from "@/assets/image/untravel.png"

export class NavigatorPathMeshGroup extends THREE.Group {
	private traveledGeometry: PathGeometry = new PathGeometry()
	private untraveledGeometry: PathGeometry = new PathGeometry()
	private traveledPathPointList: PathPointList = new PathPointList()
	private untraveledPathPointList: PathPointList = new PathPointList()
	private width: number = 1
	private progress: number = 0

	constructor(points: any) {
		super()

		this.traveledPathPointList.set(
			points,
			0.5,
			10,
			new THREE.Vector3(0, 1, 0),
			false
		)
		this.untraveledPathPointList.set(
			points.reverse(),
			0.5,
			10,
			new THREE.Vector3(0, 1, 0),
			false
		)
		this.traveledGeometry.update(this.traveledPathPointList, {
			width: 1,
			arrow: true,
			progress: 1,
		})
		this.untraveledGeometry.update(this.untraveledPathPointList, {
			width: 1,
			arrow: true,
			progress: 1,
		})
		const arrowTexture = new THREE.TextureLoader().load(travelUrl)
		arrowTexture.wrapS = arrowTexture.wrapT = THREE.RepeatWrapping
		arrowTexture.repeat.set(1, 1)
		const unTravelTexture = new THREE.TextureLoader().load(unTravelUrl)
		unTravelTexture.wrapS = THREE.RepeatWrapping
		unTravelTexture.repeat.set(-1, 1)
		unTravelTexture.colorSpace = THREE.SRGBColorSpace
		unTravelTexture.anisotropy = 32
		const untraveledMaterial = new THREE.MeshBasicMaterial({
			map: unTravelTexture,
			depthWrite: true,
			transparent: true,
			depthTest: false,
			opacity: 1,
			side: THREE.DoubleSide,
		})
		const traveledMaterial = new THREE.MeshBasicMaterial({
			map: arrowTexture,
			depthWrite: true,
			transparent: true,
			opacity: 1,
			side: THREE.DoubleSide,
		})
		const untraveledMesh = new THREE.Mesh(
			this.untraveledGeometry,
			untraveledMaterial
		)
		const traveledMesh = new THREE.Mesh(
			this.traveledGeometry,
			traveledMaterial
		)

		this.add(untraveledMesh)
		this.add(traveledMesh)
	}

	updateProgress(progress: number) {
		this.progress = progress
		this.update()
	}

	updateWidth(width: number) {
		this.width = width
		this.update()
	}

	update() {
		this.traveledGeometry.update(this.traveledPathPointList, {
			width: this.width,
			arrow: false,
			progress: this.progress,
		})
		this.untraveledGeometry.update(this.untraveledPathPointList, {
			width: this.width,
			arrow: false,
			progress: 1 - this.progress,
		})
	}

	getPointAndTangentAt(t: number) {
		return this.traveledPathPointList.getPointAndTangentAt(t)
	}

	distance() {
		return this.traveledPathPointList.distance()
	}
}
