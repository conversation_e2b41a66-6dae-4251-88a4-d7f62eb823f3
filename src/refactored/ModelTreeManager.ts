import {
	type ModelNodeData,
	type ModelTreeManager,
	type SceneNode,
	type BuildingExteriorNode,
	type BuildingFloorNode,
	ModelNodeType,
	SCENE_ROOT_NODE,
} from "./ModelTypes"

/**
 * 模型树管理器实现
 */
export class DefaultModelTreeManager implements ModelTreeManager {
	private nodes: Map<string, ModelNodeData> = new Map()

	// 🔧 新增：保存每个建筑的当前选中楼层
	private buildingCurrentFloors = new Map<string, number>()

	constructor() {
		// 初始化时添加根节点
		this.nodes.set(SCENE_ROOT_NODE.id, SCENE_ROOT_NODE)
	}

	/**
	 * 获取所有节点
	 */
	getAllNodes(): ModelNodeData[] {
		return Array.from(this.nodes.values())
	}

	/**
	 * 根据ID获取节点
	 */
	getNodeById(id: string): ModelNodeData | undefined {
		return this.nodes.get(id)
	}

	/**
	 * 获取子节点
	 */
	getChildren(parentId: string): ModelNodeData[] {
		return Array.from(this.nodes.values()).filter(
			(node) => node.parentId === parentId
		)
	}

	/**
	 * 获取根节点
	 */
	getRootNode(): SceneNode {
		return SCENE_ROOT_NODE
	}

	/**
	 * 添加节点
	 */
	addNode(node: ModelNodeData): void {
		// 验证父节点存在（除了根节点）
		if (node.parentId && !this.nodes.has(node.parentId)) {
			throw new Error(`Parent node ${node.parentId} not found`)
		}

		// 检查ID是否已存在
		if (this.nodes.has(node.id)) {
			throw new Error(`Node with id ${node.id} already exists`)
		}

		this.nodes.set(node.id, node)
	}

	/**
	 * 更新节点
	 */
	updateNode(id: string, updates: Partial<ModelNodeData>): void {
		const existingNode = this.nodes.get(id)
		if (!existingNode) {
			throw new Error(`Node ${id} not found`)
		}

		const updatedNode = {
			...existingNode,
			...updates,
			id, // 确保ID不被修改
			updatedAt: new Date(),
		} as ModelNodeData

		this.nodes.set(id, updatedNode)
	}

	/**
	 * 删除节点（递归删除子节点）
	 */
	removeNode(id: string): void {
		// 不允许删除根节点
		if (id === SCENE_ROOT_NODE.id) {
			throw new Error("Cannot remove root scene node")
		}

		// 递归删除所有子节点
		const children = this.getChildren(id)
		children.forEach((child) => this.removeNode(child.id))

		// 删除当前节点
		this.nodes.delete(id)
	}

	/**
	 * 获取建筑的所有楼层
	 */
	getBuildingFloors(buildingId: string): BuildingFloorNode[] {
		return this.getChildren(buildingId).filter(
			(node) => node.type === ModelNodeType.BUILDING_FLOOR
		) as BuildingFloorNode[]
	}

	/**
	 * 获取需要LOD的建筑
	 */
	getLODBuildings(): BuildingExteriorNode[] {
		return Array.from(this.nodes.values()).filter(
			(node) =>
				node.type === ModelNodeType.BUILDING_EXTERIOR &&
				(node as BuildingExteriorNode).hasInterior
		) as BuildingExteriorNode[]
	}

	/**
	 * 获取建筑的默认楼层
	 */
	getBuildingDefaultFloor(buildingId: string): BuildingFloorNode | undefined {
		const floors = this.getBuildingFloors(buildingId)
		return floors.find((floor) => floor.isDefault) || floors[0]
	}

	/**
	 * 设置建筑的当前显示楼层
	 */
	setBuildingCurrentFloor(buildingId: string, targetFloor: number): void {
		// 🔧 修复：保存建筑的当前选中楼层状态
		this.buildingCurrentFloors.set(buildingId, targetFloor)
		console.log(
			`📝 TreeManager保存楼层状态: ${buildingId} → 楼层 ${targetFloor}`
		)

		const floors = this.getBuildingFloors(buildingId)

		floors.forEach((floor) => {
			const shouldVisible = floor.floor === targetFloor
			if (floor.visible !== shouldVisible) {
				this.updateNode(floor.id, { visible: shouldVisible })
			}
		})
	}

	/**
	 * 获取建筑的当前选中楼层
	 */
	getBuildingCurrentFloor(buildingId: string): number | undefined {
		return this.buildingCurrentFloors.get(buildingId)
	}

	/**
	 * 检查建筑是否有选中的楼层
	 */
	hasBuildingCurrentFloor(buildingId: string): boolean {
		return this.buildingCurrentFloors.has(buildingId)
	}

	/**
	 * 获取节点的完整路径
	 */
	getNodePath(nodeId: string): ModelNodeData[] {
		const path: ModelNodeData[] = []
		let currentNode = this.getNodeById(nodeId)

		while (currentNode) {
			path.unshift(currentNode)
			if (currentNode.parentId) {
				currentNode = this.getNodeById(currentNode.parentId)
			} else {
				break
			}
		}

		return path
	}

	/**
	 * 获取节点的深度
	 */
	getNodeDepth(nodeId: string): number {
		return this.getNodePath(nodeId).length - 1
	}

	/**
	 * 检查节点是否为另一个节点的祖先
	 */
	isAncestor(ancestorId: string, descendantId: string): boolean {
		let currentNode = this.getNodeById(descendantId)

		while (currentNode && currentNode.parentId) {
			if (currentNode.parentId === ancestorId) {
				return true
			}
			currentNode = this.getNodeById(currentNode.parentId)
		}

		return false
	}

	/**
	 * 获取树的统计信息
	 */
	getTreeStats(): {
		totalNodes: number
		buildingCount: number
		floorCount: number
		modelCount: number
		lodBuildingCount: number
	} {
		const nodes = this.getAllNodes()

		return {
			totalNodes: nodes.length,
			buildingCount: nodes.filter(
				(n) => n.type === ModelNodeType.BUILDING_EXTERIOR
			).length,
			floorCount: nodes.filter(
				(n) => n.type === ModelNodeType.BUILDING_FLOOR
			).length,
			modelCount: nodes.filter((n) => n.type === ModelNodeType.MODEL)
				.length,
			lodBuildingCount: this.getLODBuildings().length,
		}
	}

	/**
	 * 导出树数据为JSON
	 */
	exportToJSON(): string {
		const data = {
			nodes: Array.from(this.nodes.values()),
			exportTime: new Date().toISOString(),
		}
		return JSON.stringify(data, null, 2)
	}

	/**
	 * 从JSON导入树数据
	 */
	importFromJSON(jsonData: string): void {
		try {
			const data = JSON.parse(jsonData)
			this.nodes.clear()

			// 重新添加根节点
			this.nodes.set(SCENE_ROOT_NODE.id, SCENE_ROOT_NODE)

			// 添加其他节点
			if (data.nodes && Array.isArray(data.nodes)) {
				data.nodes.forEach((node: ModelNodeData) => {
					if (node.id !== SCENE_ROOT_NODE.id) {
						this.nodes.set(node.id, node)
					}
				})
			}
		} catch (error) {
			throw new Error(`Failed to import tree data: ${error}`)
		}
	}
}
