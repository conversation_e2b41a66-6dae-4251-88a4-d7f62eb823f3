import * as THREE from "three"
import {
	type Annotation3DData,
	type Annotation3DManager,
	type Annotation3DRuntime,
	type Annotation3DEditState,
	type CoordinateConverter,
	Annotation3DType,
	Annotation3DEditMode,
	createAnnotation3D,
	annotationToGeoData, // 🔧 修复：添加缺失的导入
} from "./Annotation3DTypes"
import { eventBus } from "./EventBus"

/**
 * 3D标注管理器实现
 */
export class DefaultAnnotation3DManager implements Annotation3DManager {
	private scene: THREE.Scene
	private camera: THREE.Camera
	private renderer: THREE.WebGLRenderer
	private coordinateConverter: CoordinateConverter | null = null
	private annotations: Map<string, Annotation3DData> = new Map()
	private runtimeData: Map<string, Annotation3DRuntime> = new Map()
	private editState: Annotation3DEditState = {
		mode: Annotation3DEditMode.NONE,
		tempPositions: [],
		isCreating: false,
		isDragging: false,
	}
	private raycaster: THREE.Raycaster = new THREE.Raycaster()
	private annotationGroup: THREE.Group = new THREE.Group()

	constructor(
		scene: THREE.Scene,
		camera: THREE.Camera,
		renderer: THREE.WebGLRenderer,
		container: HTMLElement
	) {
		this.scene = scene
		this.camera = camera
		this.renderer = renderer

		// 创建标注组并添加到场景
		this.annotationGroup.name = "Annotations"
		this.scene.add(this.annotationGroup)

		this.setupEventListeners()
	}

	/**
	 * 设置事件监听器
	 */
	private setupEventListeners(): void {
		// 预留给未来的事件监听器
	}

	/**
	 * 设置坐标转换器
	 */
	setCoordinateConverter(converter: CoordinateConverter): void {
		this.coordinateConverter = converter
	}

	/**
	 * 生成唯一ID
	 */
	private generateId(): string {
		return `annotation_${Date.now()}_${Math.random()
			.toString(36)
			.substr(2, 9)}`
	}

	/**
	 * 创建3D标注对象
	 */
	private createAnnotation3DObject(
		annotation: Annotation3DData
	): THREE.Object3D {
		const group = new THREE.Group()
		group.name = `annotation_${annotation.id}`
		group.userData = { annotationId: annotation.id }

		switch (annotation.type) {
			case Annotation3DType.POINT:
				this.createPointAnnotation(group, annotation)
				break
			case Annotation3DType.TEXT:
				this.createTextAnnotation(group, annotation)
				break
			case Annotation3DType.LINE:
				this.createLineAnnotation(group, annotation)
				break
			case Annotation3DType.POLYGON:
				this.createPolygonAnnotation(group, annotation)
				break
		}

		group.position.copy(annotation.position)
		return group
	}

	/**
	 * 创建点标注
	 */
	private createPointAnnotation(
		group: THREE.Group,
		annotation: Annotation3DData
	): void {
		const geometry = new THREE.SphereGeometry(
			annotation.style.pointSize || 0.5,
			16,
			16
		)
		const material = new THREE.MeshBasicMaterial({
			color: annotation.style.pointColor || "#ff4444",
			transparent: true,
			opacity: annotation.style.pointOpacity || 1.0,
		})
		const sphere = new THREE.Mesh(geometry, material)

		if (annotation.style.alwaysOnTop) {
			material.depthTest = false
			sphere.renderOrder = 999
		}

		group.add(sphere)
	}

	/**
	 * 创建文字标注 - 使用Sprite实现
	 */
	private createTextAnnotation(
		group: THREE.Group,
		annotation: Annotation3DData
	): void {
		const text = annotation.text || annotation.name
		const fontSize = annotation.style.fontSize || 14
		const textColor = annotation.style.textColor || "#333333"
		const backgroundColor = annotation.style.backgroundColor || "#ffffff"

		// 创建Canvas纹理
		const canvas = document.createElement("canvas")
		const context = canvas.getContext("2d")!

		// 设置字体
		context.font = `${fontSize}px ${
			annotation.style.fontFamily || "Arial, sans-serif"
		}`

		// 测量文字尺寸
		const metrics = context.measureText(text)
		const textWidth = metrics.width
		const textHeight = fontSize

		// 设置Canvas尺寸（需要是2的幂次方以获得更好的性能）
		const padding = annotation.style.padding || 8
		canvas.width = Math.pow(
			2,
			Math.ceil(Math.log2(textWidth + padding * 2))
		)
		canvas.height = Math.pow(
			2,
			Math.ceil(Math.log2(textHeight + padding * 2))
		)

		// 重新设置字体（Canvas尺寸改变后需要重新设置）
		context.font = `${fontSize}px ${
			annotation.style.fontFamily || "Arial, sans-serif"
		}`
		context.textAlign = "center"
		context.textBaseline = "middle"

		// 绘制背景
		context.fillStyle = backgroundColor
		const borderRadius = 4
		const x = 0
		const y = 0
		const width = canvas.width
		const height = canvas.height

		context.beginPath()
		context.moveTo(x + borderRadius, y)
		context.lineTo(x + width - borderRadius, y)
		context.quadraticCurveTo(x + width, y, x + width, y + borderRadius)
		context.lineTo(x + width, y + height - borderRadius)
		context.quadraticCurveTo(
			x + width,
			y + height,
			x + width - borderRadius,
			y + height
		)
		context.lineTo(x + borderRadius, y + height)
		context.quadraticCurveTo(x, y + height, x, y + height - borderRadius)
		context.lineTo(x, y + borderRadius)
		context.quadraticCurveTo(x, y, x + borderRadius, y)
		context.closePath()
		context.fill()

		// 绘制边框
		if (annotation.style.borderWidth && annotation.style.borderWidth > 0) {
			context.strokeStyle = annotation.style.borderColor || "#cccccc"
			context.lineWidth = annotation.style.borderWidth
			context.stroke()
		}

		// 绘制文字
		context.fillStyle = textColor
		context.fillText(text, canvas.width / 2, canvas.height / 2)

		// 创建纹理
		const texture = new THREE.CanvasTexture(canvas)
		texture.needsUpdate = true

		// 创建Sprite材质
		const spriteMaterial = new THREE.SpriteMaterial({
			map: texture,
			transparent: true,
			alphaTest: 0.1,
		})

		// 创建Sprite
		const sprite = new THREE.Sprite(spriteMaterial)

		// 设置Sprite尺寸（根据文字长度调整）
		const scale = Math.max(textWidth / 100, 0.5) // 最小缩放0.5
		sprite.scale.set(scale * 2, scale, 1)

		// 稍微偏移避免重叠
		sprite.position.set(0, 1, 0)

		if (annotation.style.alwaysOnTop) {
			spriteMaterial.depthTest = false
			sprite.renderOrder = 999
		}

		group.add(sprite)
	}

	/**
	 * 创建线标注
	 */
	private createLineAnnotation(
		group: THREE.Group,
		annotation: Annotation3DData
	): void {
		if (!annotation.positions || annotation.positions.length < 2) return

		// 转换为相对坐标
		const relativePositions = annotation.positions.map((pos) =>
			pos.clone().sub(annotation.position)
		)

		// 创建线条
		const lineGeometry = new THREE.BufferGeometry().setFromPoints(
			relativePositions
		)
		const lineMaterial = new THREE.LineBasicMaterial({
			color: annotation.style.lineColor || "#0066cc",
			linewidth: annotation.style.lineWidth || 2,
			transparent: true,
			opacity: annotation.style.lineOpacity || 0.8,
		})
		const line = new THREE.Line(lineGeometry, lineMaterial)

		if (annotation.style.alwaysOnTop) {
			lineMaterial.depthTest = false
			line.renderOrder = 998
		}

		group.add(line)
	}

	/**
	 * 创建面标注
	 */
	private createPolygonAnnotation(
		group: THREE.Group,
		annotation: Annotation3DData
	): void {
		if (!annotation.positions || annotation.positions.length < 3) return

		// 转换为相对坐标
		const relativePositions = annotation.positions.map((pos) =>
			pos.clone().sub(annotation.position)
		)

		// 创建面的几何体
		const shape = new THREE.Shape()
		shape.moveTo(relativePositions[0].x, relativePositions[0].z)
		for (let i = 1; i < relativePositions.length; i++) {
			shape.lineTo(relativePositions[i].x, relativePositions[i].z)
		}
		shape.closePath()

		const geometry = new THREE.ShapeGeometry(shape)
		const material = new THREE.MeshBasicMaterial({
			color: annotation.style.lineColor || "#00aa00",
			transparent: true,
			opacity: (annotation.style.lineOpacity || 0.6) * 0.3, // 面的透明度更低
			side: THREE.DoubleSide,
		})
		const mesh = new THREE.Mesh(geometry, material)
		mesh.rotation.x = -Math.PI / 2 // 旋转到水平面
		group.add(mesh)

		// 创建边框线条
		const points = [...relativePositions, relativePositions[0]] // 闭合
		const lineGeometry = new THREE.BufferGeometry().setFromPoints(points)
		const lineMaterial = new THREE.LineBasicMaterial({
			color: annotation.style.lineColor || "#00aa00",
			linewidth: annotation.style.lineWidth || 2,
			transparent: true,
			opacity: annotation.style.lineOpacity || 0.6,
		})
		const line = new THREE.Line(lineGeometry, lineMaterial)

		if (annotation.style.alwaysOnTop) {
			material.depthTest = false
			lineMaterial.depthTest = false
			mesh.renderOrder = 997
			line.renderOrder = 998
		}

		group.add(line)

		// 添加区域标签（使用Sprite）
		const center = relativePositions
			.reduce((sum, pos) => sum.add(pos), new THREE.Vector3())
			.divideScalar(relativePositions.length)

		const text = annotation.text || annotation.name
		const fontSize = annotation.style.fontSize || 12

		// 创建Canvas纹理
		const canvas = document.createElement("canvas")
		const context = canvas.getContext("2d")!
		context.font = `${fontSize}px Arial`
		const metrics = context.measureText(text)

		canvas.width = Math.pow(2, Math.ceil(Math.log2(metrics.width + 16)))
		canvas.height = Math.pow(2, Math.ceil(Math.log2(fontSize + 8)))

		context.font = `${fontSize}px Arial`
		context.textAlign = "center"
		context.textBaseline = "middle"
		context.fillStyle =
			annotation.style.backgroundColor || "rgba(255, 255, 255, 0.9)"
		context.fillRect(0, 0, canvas.width, canvas.height)
		context.fillStyle = annotation.style.textColor || "#00aa00"
		context.fillText(text, canvas.width / 2, canvas.height / 2)

		const texture = new THREE.CanvasTexture(canvas)
		const spriteMaterial = new THREE.SpriteMaterial({
			map: texture,
			transparent: true,
		})
		const sprite = new THREE.Sprite(spriteMaterial)
		sprite.scale.set(1, 0.5, 1)
		sprite.position.copy(center)
		sprite.position.y += 0.5

		group.add(sprite)
	}

	/**
	 * 更新标注显示
	 */
	private updateAnnotationDisplay(annotation: Annotation3DData): void {
		const runtime = this.runtimeData.get(annotation.id)
		if (!runtime) return

		runtime.object3D.visible = annotation.visible
		runtime.object3D.position.copy(annotation.position)

		// 更新距离到相机
		runtime.distanceToCamera = this.camera.position.distanceTo(
			annotation.position
		)

		// 处理距离淡化
		if (annotation.style.fadeWithDistance && annotation.visible) {
			const minDist = annotation.style.minDistance || 10
			const maxDist = annotation.style.maxDistance || 1000
			const alpha = THREE.MathUtils.clamp(
				1 - (runtime.distanceToCamera - minDist) / (maxDist - minDist),
				0,
				1
			)

			runtime.object3D.traverse((child) => {
				if (child instanceof THREE.Mesh && child.material) {
					const material = child.material as THREE.Material
					if ("opacity" in material) {
						material.opacity = alpha
						material.transparent = alpha < 1
					}
				}
			})
		}
	}

	// 实现Annotation3DManager接口
	addAnnotation(
		annotation: Omit<Annotation3DData, "id" | "createdAt" | "updatedAt">
	): string {
		const id = this.generateId()
		const now = new Date()

		const annotationData: Annotation3DData = {
			...annotation,
			id,
			createdAt: now,
			updatedAt: now,
		}

		this.annotations.set(id, annotationData)

		// 创建3D对象
		const object3D = this.createAnnotation3DObject(annotationData)
		this.annotationGroup.add(object3D)

		// 创建运行时数据
		const runtime: Annotation3DRuntime = {
			data: annotationData,
			object3D,
			isVisible: annotationData.visible,
			distanceToCamera: 0,
		}
		this.runtimeData.set(id, runtime)

		this.updateAnnotationDisplay(annotationData)
		this.emit("annotation-added", {
			annotationId: id,
			annotation: annotationData,
		})

		return id
	}

	updateAnnotation(id: string, updates: Partial<Annotation3DData>): void {
		const annotation = this.annotations.get(id)
		if (!annotation) return

		const updatedAnnotation = {
			...annotation,
			...updates,
			id, // 确保ID不被修改
			updatedAt: new Date(),
		}

		this.annotations.set(id, updatedAnnotation)

		// 重新创建3D对象
		const runtime = this.runtimeData.get(id)
		if (runtime) {
			this.annotationGroup.remove(runtime.object3D)
			const newObject3D = this.createAnnotation3DObject(updatedAnnotation)
			this.annotationGroup.add(newObject3D)
			runtime.object3D = newObject3D
			runtime.data = updatedAnnotation
		}

		this.updateAnnotationDisplay(updatedAnnotation)
		this.emit("annotation-updated", {
			annotationId: id,
			annotation: updatedAnnotation,
		})
	}

	deleteAnnotation(id: string): void {
		const annotation = this.annotations.get(id)
		if (!annotation) return

		const runtime = this.runtimeData.get(id)
		if (runtime) {
			this.annotationGroup.remove(runtime.object3D)
			this.runtimeData.delete(id)
		}

		this.annotations.delete(id)
		this.emit("annotation-deleted", { annotationId: id, annotation })
	}

	getAnnotation(id: string): Annotation3DData | undefined {
		return this.annotations.get(id)
	}

	getAllAnnotations(): Annotation3DData[] {
		return Array.from(this.annotations.values())
	}

	setAnnotationVisibility(id: string, visible: boolean): void {
		this.updateAnnotation(id, { visible })
	}

	showAllAnnotations(): void {
		this.annotations.forEach((annotation) => {
			this.updateAnnotation(annotation.id, { visible: true })
		})
	}

	hideAllAnnotations(): void {
		this.annotations.forEach((annotation) => {
			this.updateAnnotation(annotation.id, { visible: false })
		})
	}

	getAnnotationsByCategory(category: string): Annotation3DData[] {
		return Array.from(this.annotations.values()).filter(
			(annotation) => annotation.category === category
		)
	}

	setAnnotationCategory(id: string, category: string): void {
		this.updateAnnotation(id, { category })
	}

	startEdit(mode: Annotation3DEditMode, annotationId?: string): void {
		this.editState = {
			mode,
			activeAnnotationId: annotationId,
			tempPositions: [],
			isCreating: false,
			isDragging: false,
		}

		this.emit("edit-started", { mode, annotationId })
	}

	stopEdit(): void {
		this.editState = {
			mode: Annotation3DEditMode.NONE,
			tempPositions: [],
			isCreating: false,
			isDragging: false,
		}

		this.emit("edit-stopped")
	}

	getEditState(): Annotation3DEditState {
		return { ...this.editState }
	}

	updateCameraPosition(camera: THREE.Camera): void {
		this.camera = camera

		// 更新所有标注的显示
		this.annotations.forEach((annotation) => {
			this.updateAnnotationDisplay(annotation)
		})
	}

	handleClick(intersectionPoint: THREE.Vector3, camera: THREE.Camera): void {
		if (this.editState.mode === Annotation3DEditMode.NONE) return

		switch (this.editState.mode) {
			case Annotation3DEditMode.ADD_POINT:
				this.addPointAnnotation(intersectionPoint)
				break
			case Annotation3DEditMode.ADD_TEXT:
				this.addTextAnnotation(intersectionPoint)
				break
			case Annotation3DEditMode.ADD_LINE:
				this.addLinePoint(intersectionPoint)
				break
			case Annotation3DEditMode.ADD_POLYGON:
				this.addPolygonPoint(intersectionPoint)
				break
		}
	}

	handleMouseMove(intersectionPoint: THREE.Vector3): void {
		// 处理鼠标移动，可用于实时预览
	}

	private addPointAnnotation(position: THREE.Vector3): void {
		const annotation = createAnnotation3D(
			`点标注 ${this.annotations.size + 1}`,
			Annotation3DType.POINT,
			position
		)

		this.addAnnotation(annotation)
		this.stopEdit()
	}

	private addTextAnnotation(position: THREE.Vector3): void {
		const annotation = createAnnotation3D(
			`文字标注 ${this.annotations.size + 1}`,
			Annotation3DType.TEXT,
			position,
			{ text: "新建标注" }
		)

		this.addAnnotation(annotation)
		this.stopEdit()
	}

	private addLinePoint(position: THREE.Vector3): void {
		this.editState.tempPositions.push(position.clone())
		this.editState.isCreating = true

		// 至少需要2个点才能创建线
		if (this.editState.tempPositions.length >= 2) {
			// 右键或双击完成线的绘制，这里简化为自动完成
			// 实际应用中可以监听键盘事件或右键事件
			if (this.editState.tempPositions.length >= 2) {
				this.finishLineCreation()
			}
		}
	}

	private addPolygonPoint(position: THREE.Vector3): void {
		this.editState.tempPositions.push(position.clone())
		this.editState.isCreating = true

		// 至少需要3个点才能创建面
		if (this.editState.tempPositions.length >= 3) {
			// 右键或双击完成面的绘制，这里简化为自动完成
			// 实际应用中可以监听键盘事件或右键事件
			if (this.editState.tempPositions.length >= 3) {
				this.finishPolygonCreation()
			}
		}
	}

	private finishLineCreation(): void {
		if (this.editState.tempPositions.length < 2) return

		const annotation = createAnnotation3D(
			`线标注 ${this.annotations.size + 1}`,
			Annotation3DType.LINE,
			this.editState.tempPositions[0], // 使用第一个点作为基准位置
			{
				positions: [...this.editState.tempPositions],
			}
		)

		this.addAnnotation(annotation)
		this.stopEdit()
	}

	private finishPolygonCreation(): void {
		if (this.editState.tempPositions.length < 3) return

		const annotation = createAnnotation3D(
			`面标注 ${this.annotations.size + 1}`,
			Annotation3DType.POLYGON,
			this.editState.tempPositions[0], // 使用第一个点作为基准位置
			{
				positions: [...this.editState.tempPositions],
				text: `面积: ${this.calculatePolygonArea(
					this.editState.tempPositions
				).toFixed(2)} m²`,
			}
		)

		this.addAnnotation(annotation)
		this.stopEdit()
	}

	private calculatePolygonArea(positions: THREE.Vector3[]): number {
		if (positions.length < 3) return 0

		let area = 0
		for (let i = 0; i < positions.length; i++) {
			const j = (i + 1) % positions.length
			area += positions[i].x * positions[j].z
			area -= positions[j].x * positions[i].z
		}
		return Math.abs(area) / 2
	}

	exportToJSON(): string {
		const data = {
			annotations: Array.from(this.annotations.values()).map(
				(annotation) => ({
					...annotation,
					position: annotation.position.toArray(),
					endPosition: annotation.endPosition?.toArray(),
					positions: annotation.positions?.map((p) => p.toArray()),
				})
			),
			exportTime: new Date().toISOString(),
		}
		return JSON.stringify(data, null, 2)
	}

	// 移除了地理坐标格式导出功能

	/**
	 * 导出为标准GeoJSON格式
	 */
	exportToStandardGeoJSON(): string {
		if (!this.coordinateConverter) {
			throw new Error("坐标转换器未设置，无法导出地理坐标格式")
		}

		const features = Array.from(this.annotations.values())
			.filter((annotation) => annotation.visible)
			.map((annotation) => {
				const geoData = annotationToGeoData(
					annotation,
					this.coordinateConverter!
				)

				// 根据标注类型创建不同的几何体
				let geometry: any

				switch (annotation.type) {
					case "point":
						geometry = {
							type: "Point",
							coordinates: [
								geoData.geoPosition.longitude,
								geoData.geoPosition.latitude,
								geoData.geoPosition.altitude || 0,
							],
						}
						break

					case "line":
						if (
							geoData.geoPositions &&
							geoData.geoPositions.length >= 2
						) {
							geometry = {
								type: "LineString",
								coordinates: geoData.geoPositions.map((pos) => [
									pos.longitude,
									pos.latitude,
									pos.altitude || 0,
								]),
							}
						} else {
							// 如果没有足够的点，创建一个点
							geometry = {
								type: "Point",
								coordinates: [
									geoData.geoPosition.longitude,
									geoData.geoPosition.latitude,
									geoData.geoPosition.altitude || 0,
								],
							}
						}
						break

					case "polygon":
						if (
							geoData.geoPositions &&
							geoData.geoPositions.length >= 3
						) {
							// 确保多边形是闭合的
							const coordinates = geoData.geoPositions.map(
								(pos) => [
									pos.longitude,
									pos.latitude,
									pos.altitude || 0,
								]
							)
							// 如果第一个点和最后一个点不相同，则闭合多边形
							const first = coordinates[0]
							const last = coordinates[coordinates.length - 1]
							if (first[0] !== last[0] || first[1] !== last[1]) {
								coordinates.push([...first])
							}

							geometry = {
								type: "Polygon",
								coordinates: [coordinates],
							}
						} else {
							// 如果没有足够的点，创建一个点
							geometry = {
								type: "Point",
								coordinates: [
									geoData.geoPosition.longitude,
									geoData.geoPosition.latitude,
									geoData.geoPosition.altitude || 0,
								],
							}
						}
						break

					default:
						// 默认创建点
						geometry = {
							type: "Point",
							coordinates: [
								geoData.geoPosition.longitude,
								geoData.geoPosition.latitude,
								geoData.geoPosition.altitude || 0,
							],
						}
				}

				return {
					type: "Feature",
					id: annotation.id,
					geometry,
					properties: {
						name: annotation.name,
						description: annotation.description || "",
						text: annotation.text || "",
						annotationType: annotation.type,
						category: annotation.category || "",
						tags: annotation.tags || [],
						style: annotation.style,
						createdAt: annotation.createdAt.toISOString(),
						updatedAt: annotation.updatedAt.toISOString(),
						...annotation.properties,
					},
				}
			})

		const geojson = {
			type: "FeatureCollection",
			features,
			metadata: {
				exportTime: new Date().toISOString(),
				coordinateSystem: "WGS84",
				source: "3D Annotation System",
				converterConfig: this.coordinateConverter.getMapCenter
					? { mapCenter: this.coordinateConverter.getMapCenter() }
					: null,
			},
		}

		return JSON.stringify(geojson, null, 2)
	}

	importFromJSON(jsonData: string): void {
		try {
			const data = JSON.parse(jsonData)
			if (data.annotations && Array.isArray(data.annotations)) {
				data.annotations.forEach((annotationData: any) => {
					const annotation = {
						...annotationData,
						position: new THREE.Vector3().fromArray(
							annotationData.position
						),
						endPosition: annotationData.endPosition
							? new THREE.Vector3().fromArray(
									annotationData.endPosition
							  )
							: undefined,
						positions: annotationData.positions?.map(
							(p: number[]) => new THREE.Vector3().fromArray(p)
						),
					}
					delete annotation.id
					delete annotation.createdAt
					delete annotation.updatedAt

					this.addAnnotation(annotation)
				})
			}
		} catch (error) {
			throw new Error(`Failed to import annotation data: ${error}`)
		}
	}

	// 移除了地理坐标格式导入功能

	// 事件系统
	on(event: string, handler: (...args: any[]) => void): void {
		eventBus.on(event, handler)
	}

	off(event: string, handler: (...args: any[]) => void): void {
		eventBus.off(event, handler)
	}

	emit(event: string, ...args: any[]): void {
		eventBus.emit(event, ...args)
	}

	dispose(): void {
		// 清理3D对象
		this.scene.remove(this.annotationGroup)
		this.runtimeData.clear()
		this.annotations.clear()
	}
}
