import * as THREE from "three"
import mapboxgl from "mapbox-gl"
import type { CoordinateConverter, GeoCoordinate } from "./Annotation3DTypes"
import {
	SceneConfig,
	MAPBOX_CONVERTER_CONFIG,
	COORDINATE_CONVERTER_CONFIG,
} from "./SceneConfig"

/**
 * Mapbox坐标转换器
 * 将Three.js世界坐标与地理坐标（经纬度）进行转换
 */
export class MapboxCoordinateConverter implements CoordinateConverter {
	private map: mapboxgl.Map
	private modelOrigin: [number, number] // 模型原点的经纬度
	private modelAltitude: number // 模型基准海拔
	private scale: number // 缩放比例

	constructor(
		map: mapboxgl.Map,
		modelOrigin: [number, number] = SceneConfig.getMapCenter(), // 🔧 统一：使用场景配置
		modelAltitude: number = SceneConfig.getModelAltitude(), // 🔧 统一：使用场景配置
		scale: number = SceneConfig.getDefaultScale() // 🔧 统一：使用场景配置
	) {
		this.map = map
		this.modelOrigin = modelOrigin
		this.modelAltitude = modelAltitude
		this.scale = scale
	}

	/**
	 * 世界坐标转地理坐标
	 */
	worldToGeo(worldPos: THREE.Vector3): GeoCoordinate {
		// 将Three.js坐标转换为Mapbox的墨卡托坐标
		const mercatorOrigin = mapboxgl.MercatorCoordinate.fromLngLat(
			this.modelOrigin
		)

		// 计算相对于原点的偏移（考虑缩放）
		const offsetX = worldPos.x / this.scale
		const offsetZ = -worldPos.z / this.scale // Three.js的Z轴与地图的Y轴相反

		// 转换为墨卡托坐标系的偏移
		// 这里需要根据地球半径和缩放级别进行转换
		const zoom = this.map.getZoom()
		const mercatorScale =
			(Math.pow(2, zoom) * 256) / (2 * Math.PI * 6378137) // 地球半径

		const mercatorX = mercatorOrigin.x + offsetX * mercatorScale
		const mercatorY = mercatorOrigin.y + offsetZ * mercatorScale

		// 转换回经纬度
		const lngLat = new mapboxgl.MercatorCoordinate(
			mercatorX,
			mercatorY,
			0
		).toLngLat()

		return {
			longitude: lngLat.lng,
			latitude: lngLat.lat,
			altitude: this.modelAltitude + worldPos.y / this.scale,
		}
	}

	/**
	 * 地理坐标转世界坐标
	 */
	geoToWorld(geoPos: GeoCoordinate): THREE.Vector3 {
		// 将经纬度转换为墨卡托坐标
		const mercatorPos = mapboxgl.MercatorCoordinate.fromLngLat([
			geoPos.longitude,
			geoPos.latitude,
		])
		const mercatorOrigin = mapboxgl.MercatorCoordinate.fromLngLat(
			this.modelOrigin
		)

		// 计算墨卡托坐标的偏移
		const mercatorOffsetX = mercatorPos.x - mercatorOrigin.x
		const mercatorOffsetY = mercatorPos.y - mercatorOrigin.y

		// 转换为Three.js世界坐标
		const zoom = this.map.getZoom()
		const mercatorScale =
			(Math.pow(2, zoom) * 256) / (2 * Math.PI * 6378137)

		const worldX = (mercatorOffsetX / mercatorScale) * this.scale
		const worldZ = (-mercatorOffsetY / mercatorScale) * this.scale // 注意Z轴方向
		const worldY =
			((geoPos.altitude || 0) - this.modelAltitude) * this.scale

		return new THREE.Vector3(worldX, worldY, worldZ)
	}

	/**
	 * 获取地图中心点
	 */
	getMapCenter(): GeoCoordinate {
		const center = this.map.getCenter()
		return {
			longitude: center.lng,
			latitude: center.lat,
			altitude: this.modelAltitude,
		}
	}

	/**
	 * 设置模型原点
	 */
	setModelOrigin(origin: [number, number]): void {
		this.modelOrigin = origin
	}

	/**
	 * 设置模型基准海拔
	 */
	setModelAltitude(altitude: number): void {
		this.modelAltitude = altitude
	}

	/**
	 * 设置缩放比例
	 */
	setScale(scale: number): void {
		this.scale = scale
	}

	/**
	 * 获取当前配置
	 */
	getConfig() {
		return {
			modelOrigin: this.modelOrigin,
			modelAltitude: this.modelAltitude,
			scale: this.scale,
			mapCenter: this.getMapCenter(),
			mapZoom: this.map.getZoom(),
		}
	}
}

/**
 * 简化的坐标转换器（用于测试或简单场景）
 * 使用线性近似转换，适用于小范围区域
 */
export class SimpleCoordinateConverter implements CoordinateConverter {
	private originLng: number
	private originLat: number
	private altitude: number
	private scale: number

	// 地球半径（米）
	private static readonly EARTH_RADIUS = 6378137

	constructor(
		originLng: number = SceneConfig.getMapCenter()[0], // 🔧 统一：使用场景配置
		originLat: number = SceneConfig.getMapCenter()[1], // 🔧 统一：使用场景配置
		altitude: number = SceneConfig.getBaseAltitude(), // 🔧 统一：使用场景配置
		scale: number = SceneConfig.getDefaultScale() // 🔧 统一：使用场景配置
	) {
		this.originLng = originLng
		this.originLat = originLat
		this.altitude = altitude
		this.scale = scale
	}

	worldToGeo(worldPos: THREE.Vector3): GeoCoordinate {
		// 将Three.js坐标转换为米
		const metersX = worldPos.x / this.scale
		const metersZ = -worldPos.z / this.scale // Z轴方向相反

		// 转换为经纬度偏移
		const latRadians = (this.originLat * Math.PI) / 180
		const lngOffset =
			((metersX /
				(SimpleCoordinateConverter.EARTH_RADIUS *
					Math.cos(latRadians))) *
				180) /
			Math.PI
		const latOffset =
			((metersZ / SimpleCoordinateConverter.EARTH_RADIUS) * 180) / Math.PI

		// 计算最终坐标
		let longitude = this.originLng + lngOffset
		let latitude = this.originLat + latOffset

		// 确保经纬度在有效范围内
		longitude = Math.max(-180, Math.min(180, longitude))
		latitude = Math.max(-90, Math.min(90, latitude))

		return {
			longitude,
			latitude,
			altitude: this.altitude + worldPos.y / this.scale,
		}
	}

	geoToWorld(geoPos: GeoCoordinate): THREE.Vector3 {
		// 计算经纬度偏移
		const lngOffset = geoPos.longitude - this.originLng
		const latOffset = geoPos.latitude - this.originLat

		// 转换为米
		const latRadians = (this.originLat * Math.PI) / 180
		const metersX =
			(lngOffset *
				SimpleCoordinateConverter.EARTH_RADIUS *
				Math.cos(latRadians) *
				Math.PI) /
			180
		const metersZ =
			(latOffset * SimpleCoordinateConverter.EARTH_RADIUS * Math.PI) / 180

		return new THREE.Vector3(
			metersX * this.scale,
			((geoPos.altitude || 0) - this.altitude) * this.scale,
			-metersZ * this.scale // Z轴方向相反
		)
	}

	getMapCenter(): GeoCoordinate {
		return {
			longitude: this.originLng,
			latitude: this.originLat,
			altitude: this.altitude,
		}
	}

	setOrigin(lng: number, lat: number): void {
		this.originLng = lng
		this.originLat = lat
	}

	setAltitude(altitude: number): void {
		this.altitude = altitude
	}

	setScale(scale: number): void {
		this.scale = scale
	}
}

/**
 * 工具函数：格式化地理坐标显示
 */
export function formatGeoCoordinate(coord: GeoCoordinate): string {
	const lng = coord.longitude.toFixed(6)
	const lat = coord.latitude.toFixed(6)
	const alt = coord.altitude ? `, ${coord.altitude.toFixed(2)}m` : ""
	return `${lng}, ${lat}${alt}`
}

/**
 * 工具函数：计算两个地理坐标间的距离（米）
 */
export function calculateGeoDistance(
	coord1: GeoCoordinate,
	coord2: GeoCoordinate
): number {
	const R = 6378137 // 地球半径（米）
	const lat1Rad = (coord1.latitude * Math.PI) / 180
	const lat2Rad = (coord2.latitude * Math.PI) / 180
	const deltaLatRad = ((coord2.latitude - coord1.latitude) * Math.PI) / 180
	const deltaLngRad = ((coord2.longitude - coord1.longitude) * Math.PI) / 180

	const a =
		Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
		Math.cos(lat1Rad) *
			Math.cos(lat2Rad) *
			Math.sin(deltaLngRad / 2) *
			Math.sin(deltaLngRad / 2)
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

	return R * c
}

/**
 * 工具函数：计算地理坐标多边形的面积（平方米）
 */
export function calculateGeoPolygonArea(coordinates: GeoCoordinate[]): number {
	if (coordinates.length < 3) return 0

	const R = 6378137 // 地球半径（米）
	let area = 0

	for (let i = 0; i < coordinates.length; i++) {
		const j = (i + 1) % coordinates.length
		const lat1 = (coordinates[i].latitude * Math.PI) / 180
		const lat2 = (coordinates[j].latitude * Math.PI) / 180
		const lng1 = (coordinates[i].longitude * Math.PI) / 180
		const lng2 = (coordinates[j].longitude * Math.PI) / 180

		area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2))
	}

	return Math.abs((area * R * R) / 2)
}

/**
 * 基于旧代码threeToLngLat实现的精确坐标转换器
 * 使用Mapbox墨卡托坐标系统和模型旋转
 */
export class AccurateCoordinateConverter implements CoordinateConverter {
	private mapCenter: [number, number]
	private modelRotate: [number, number, number]
	private altitude: number

	constructor(
		mapCenter: [number, number] = SceneConfig.getMapCenter(), // 🔧 统一：使用场景配置
		modelRotate: [number, number, number] = SceneConfig.getModelRotation(), // 🔧 统一：使用场景配置
		altitude: number = SceneConfig.getBaseAltitude() // 🔧 统一：使用场景配置
	) {
		this.mapCenter = mapCenter
		this.modelRotate = modelRotate
		this.altitude = altitude
	}

	worldToGeo(worldPos: THREE.Vector3): GeoCoordinate {
		// 完全按照旧代码的threeToLngLat函数实现
		// console.log("转换3D坐标到地理坐标:", worldPos)

		// 1. 应用模型旋转 - 使用与render方法完全一致的旋转矩阵构建方式
		const rotationX = new THREE.Matrix4().makeRotationAxis(
			new THREE.Vector3(1, 0, 0),
			this.modelRotate[0]
		)
		const rotationY = new THREE.Matrix4().makeRotationAxis(
			new THREE.Vector3(0, 1, 0),
			this.modelRotate[1]
		)
		const rotationZ = new THREE.Matrix4().makeRotationAxis(
			new THREE.Vector3(0, 0, 1),
			this.modelRotate[2]
		)
		// 按照render方法的顺序：Z -> X -> Y
		const matrix = new THREE.Matrix4()
			.multiply(rotationZ)
			.multiply(rotationX)
			.multiply(rotationY)
		const rotatedPos = worldPos.clone().applyMatrix4(matrix)
		// console.log("旋转后坐标:", rotatedPos)

		// 2. 获取模型原点的墨卡托坐标
		// 🔧 关键修复：使用模型海拔而不是0
		const modelOrigin = mapboxgl.MercatorCoordinate.fromLngLat(
			this.mapCenter,
			SceneConfig.getModelAltitude() // 使用统一的模型海拔
		)
		const scale = modelOrigin.meterInMercatorCoordinateUnits()
		// console.log("模型原点:", modelOrigin, "缩放:", scale)

		// 3. 转换为墨卡托坐标（完全按照旧代码逻辑）
		const x = rotatedPos.x * scale + modelOrigin.x
		const y = -rotatedPos.y * scale + modelOrigin.y // 🔧 关键修复：添加负号，与旧版本完全一致
		const z = rotatedPos.z * scale + modelOrigin.z // 高度使用z

		// console.log("墨卡托坐标:", { x, y, z })

		// 4. 转换为经纬度
		const mercator = new mapboxgl.MercatorCoordinate(x, y, z)
		const lngLat = mercator.toLngLat()
		// console.log("转换结果:", lngLat)

		// 确保经纬度在有效范围内
		const longitude = Math.max(-180, Math.min(180, lngLat.lng))
		const latitude = Math.max(-90, Math.min(90, lngLat.lat))

		return {
			longitude,
			latitude,
			altitude: this.altitude + rotatedPos.z, // 🔧 修复：使用旋转后的Z坐标作为高度（与新的映射一致）
		}
	}

	geoToWorld(geoPos: GeoCoordinate): THREE.Vector3 {
		// 反向转换：从经纬度到Three.js世界坐标
		console.log("转换地理坐标到3D坐标:", geoPos)

		const mercator = mapboxgl.MercatorCoordinate.fromLngLat([
			geoPos.longitude,
			geoPos.latitude,
		])
		const modelOrigin = mapboxgl.MercatorCoordinate.fromLngLat(
			this.mapCenter,
			SceneConfig.getModelAltitude() // 🔧 关键修复：使用统一的模型海拔
		)
		const scale = modelOrigin.meterInMercatorCoordinateUnits()

		// 按照worldToGeo的逆向逻辑
		const x = (mercator.x - modelOrigin.x) / scale
		const y_merc = (mercator.y - modelOrigin.y) / scale // 墨卡托Y

		// 🔧 修复坐标轴映射：与worldToGeo完全对应（包括负号）
		const worldPos = new THREE.Vector3(
			x, // X保持不变
			-y_merc, // Y是负的墨卡托Y（对应worldToGeo中的-rotatedPos.y）
			(geoPos.altitude || 0) - this.altitude // Z是高度（对应worldToGeo中的rotatedPos.z）
		)

		console.log("转换前世界坐标:", worldPos)

		// 应用反向旋转 - 使用与render方法完全一致的旋转矩阵构建方式
		const rotationX = new THREE.Matrix4().makeRotationAxis(
			new THREE.Vector3(1, 0, 0),
			this.modelRotate[0]
		)
		const rotationY = new THREE.Matrix4().makeRotationAxis(
			new THREE.Vector3(0, 1, 0),
			this.modelRotate[1]
		)
		const rotationZ = new THREE.Matrix4().makeRotationAxis(
			new THREE.Vector3(0, 0, 1),
			this.modelRotate[2]
		)
		// 反向旋转：按照相反的顺序 Y -> X -> Z，并且取负角度
		const inverseMatrix = new THREE.Matrix4()
			.multiply(
				rotationY
					.clone()
					.makeRotationAxis(
						new THREE.Vector3(0, 1, 0),
						-this.modelRotate[1]
					)
			)
			.multiply(
				rotationX
					.clone()
					.makeRotationAxis(
						new THREE.Vector3(1, 0, 0),
						-this.modelRotate[0]
					)
			)
			.multiply(
				rotationZ
					.clone()
					.makeRotationAxis(
						new THREE.Vector3(0, 0, 1),
						-this.modelRotate[2]
					)
			)
		worldPos.applyMatrix4(inverseMatrix)

		console.log("反向旋转后坐标:", worldPos)
		return worldPos
	}

	getMapCenter(): GeoCoordinate {
		return {
			longitude: this.mapCenter[0],
			latitude: this.mapCenter[1],
			altitude: this.altitude,
		}
	}

	setMapCenter(center: [number, number]): void {
		this.mapCenter = center
	}

	setModelRotation(rotation: [number, number, number]): void {
		this.modelRotate = rotation
	}

	setAltitude(altitude: number): void {
		this.altitude = altitude
	}

	/**
	 * 测试坐标转换的往返精度
	 */
	testRoundTripAccuracy(testPoint: THREE.Vector3): {
		original: THREE.Vector3
		geoCoordinate: GeoCoordinate
		converted: THREE.Vector3
		error: number
	} {
		console.log("🧪 测试坐标转换往返精度")
		console.log("原始3D坐标:", testPoint)

		// 3D -> 地理坐标
		const geoCoordinate = this.worldToGeo(testPoint)
		console.log("转换后地理坐标:", geoCoordinate)

		// 地理坐标 -> 3D
		const convertedPoint = this.geoToWorld(geoCoordinate)
		console.log("往返后3D坐标:", convertedPoint)

		// 计算误差
		const error = testPoint.distanceTo(convertedPoint)
		console.log("往返误差:", error, "米")

		return {
			original: testPoint,
			geoCoordinate,
			converted: convertedPoint,
			error,
		}
	}
}
