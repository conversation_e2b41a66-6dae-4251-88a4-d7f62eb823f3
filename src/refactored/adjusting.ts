import { Matrix, SingularValueDecomposition } from "ml-matrix"
import mapboxgl from "mapbox-gl"

type LngLat = [number, number]
type Vec2 = [number, number]

/**
 * 精确的地理参考求解器
 * 根据已知的控制点对应关系，求解真实的地图中心和旋转角度
 */
export class GeoreferenceResolver {
	/**
	 * 求解地图中心和旋转角度
	 * @param controlPoints 控制点数据
	 * @returns 地图中心坐标和旋转角度
	 */
	static solve(controlPoints: {
		geographic: LngLat[] // 地理坐标（经纬度）
		model: Vec2[] // 模型坐标（3D场景中的XZ坐标）
	}): {
		mapCenter: LngLat
		rotationDeg: number
		scale: number
		residualError: number
	} {
		const { geographic, model } = controlPoints

		if (geographic.length !== model.length) {
			throw new Error("控制点数量不匹配")
		}

		if (geographic.length < 2) {
			throw new Error("至少需要2个控制点")
		}

		console.log("=== 地理参考求解开始 ===")
		console.log("地理坐标控制点:", geographic)
		console.log("模型坐标控制点:", model)

		// 1. 将地理坐标转换为平面坐标（使用Web墨卡托投影）
		const worldCoords = this.geoToWorldCoords(geographic)
		console.log("转换后的世界坐标:", worldCoords)

		// 2. 使用最小二乘法求解相似变换参数
		const transform = this.solveSimilarityTransform(model, worldCoords)
		console.log("求解的变换参数:", transform)

		// 3. 从变换参数中提取地图中心和旋转角度
		const result = this.extractMapCenterAndRotation(
			transform,
			geographic[0]
		)
		console.log("最终结果:", result)

		return result
	}

	/**
	 * 将地理坐标转换为世界坐标（米）
	 */
	private static geoToWorldCoords(geoCoords: LngLat[]): Vec2[] {
		// 选择第一个点作为参考原点
		const origin = geoCoords[0]
		const originMerc = mapboxgl.MercatorCoordinate.fromLngLat({
			lng: origin[0],
			lat: origin[1],
		})
		const scale = originMerc.meterInMercatorCoordinateUnits()

		return geoCoords.map(([lng, lat]) => {
			const merc = mapboxgl.MercatorCoordinate.fromLngLat({ lng, lat })

			// 转换为相对于原点的米坐标
			const x = (merc.x - originMerc.x) / scale
			const y = (merc.y - originMerc.y) / scale

			return [x, y]
		})
	}

	/**
	 * 求解相似变换参数（旋转、缩放、平移）
	 * 使用最小二乘法求解 P' = sR*P + t
	 */
	private static solveSimilarityTransform(
		sourcePoints: Vec2[], // 模型坐标
		targetPoints: Vec2[] // 世界坐标
	): {
		rotation: number // 旋转角度（弧度）
		scale: number // 缩放比例
		translation: Vec2 // 平移向量
		residualError: number // 残差误差
	} {
		const n = sourcePoints.length

		// 计算质心
		const sourceCentroid = this.calculateCentroid(sourcePoints)
		const targetCentroid = this.calculateCentroid(targetPoints)

		console.log("源点质心:", sourceCentroid)
		console.log("目标点质心:", targetCentroid)

		// 去中心化
		const sourceCentered = sourcePoints.map(
			([x, y]) => [x - sourceCentroid[0], y - sourceCentroid[1]] as Vec2
		)

		const targetCentered = targetPoints.map(
			([x, y]) => [x - targetCentroid[0], y - targetCentroid[1]] as Vec2
		)

		// 计算缩放比例
		const sourceScale = Math.sqrt(
			sourceCentered.reduce((sum, [x, y]) => sum + x * x + y * y, 0) / n
		)
		const targetScale = Math.sqrt(
			targetCentered.reduce((sum, [x, y]) => sum + x * x + y * y, 0) / n
		)
		const scale = targetScale / sourceScale

		console.log("计算的缩放比例:", scale)

		// 使用SVD求解旋转矩阵
		const H = new Matrix(2, 2)
		for (let i = 0; i < n; i++) {
			const [sx, sy] = sourceCentered[i]
			const [tx, ty] = targetCentered[i]

			H.set(0, 0, H.get(0, 0) + sx * tx)
			H.set(0, 1, H.get(0, 1) + sx * ty)
			H.set(1, 0, H.get(1, 0) + sy * tx)
			H.set(1, 1, H.get(1, 1) + sy * ty)
		}

		const svd = new SingularValueDecomposition(H)
		const U = svd.leftSingularVectors
		const V = svd.rightSingularVectors

		let R = V.mmul(U.transpose())

		// 确保是旋转矩阵（行列式为1）
		const det = R.get(0, 0) * R.get(1, 1) - R.get(0, 1) * R.get(1, 0)
		if (det < 0) {
			const Vmod = V.clone()
			Vmod.set(0, 1, -Vmod.get(0, 1))
			Vmod.set(1, 1, -Vmod.get(1, 1))
			R = Vmod.mmul(U.transpose())
		}

		// 提取旋转角度
		const rotation = Math.atan2(R.get(1, 0), R.get(0, 0))

		// 计算平移
		const rotatedScaledCentroid = [
			scale *
				(Math.cos(rotation) * sourceCentroid[0] -
					Math.sin(rotation) * sourceCentroid[1]),
			scale *
				(Math.sin(rotation) * sourceCentroid[0] +
					Math.cos(rotation) * sourceCentroid[1]),
		] as Vec2

		const translation = [
			targetCentroid[0] - rotatedScaledCentroid[0],
			targetCentroid[1] - rotatedScaledCentroid[1],
		] as Vec2

		// 计算残差误差
		let totalError = 0
		for (let i = 0; i < n; i++) {
			const [sx, sy] = sourcePoints[i]
			const [tx, ty] = targetPoints[i]

			// 应用变换
			const transformedX =
				scale * (Math.cos(rotation) * sx - Math.sin(rotation) * sy) +
				translation[0]
			const transformedY =
				scale * (Math.sin(rotation) * sx + Math.cos(rotation) * sy) +
				translation[1]

			// 计算误差
			const errorX = transformedX - tx
			const errorY = transformedY - ty
			totalError += Math.sqrt(errorX * errorX + errorY * errorY)
		}

		const residualError = totalError / n

		return {
			rotation,
			scale,
			translation,
			residualError,
		}
	}

	/**
	 * 计算点集的质心
	 */
	private static calculateCentroid(points: Vec2[]): Vec2 {
		const sum = points.reduce(
			([sumX, sumY], [x, y]) => [sumX + x, sumY + y],
			[0, 0]
		)
		return [sum[0] / points.length, sum[1] / points.length]
	}

	/**
	 * 从变换参数中提取地图中心和旋转角度
	 */
	private static extractMapCenterAndRotation(
		transform: {
			rotation: number
			scale: number
			translation: Vec2
			residualError: number
		},
		referenceGeoPoint: LngLat
	): {
		mapCenter: LngLat
		rotationDeg: number
		scale: number
		residualError: number
	} {
		// 计算地图中心
		// 地图中心是模型原点(0,0)对应的地理坐标
		const originMerc = mapboxgl.MercatorCoordinate.fromLngLat({
			lng: referenceGeoPoint[0],
			lat: referenceGeoPoint[1],
		})
		const meterScale = originMerc.meterInMercatorCoordinateUnits()

		// 模型原点经过变换后在世界坐标系中的位置
		const worldOriginX = transform.translation[0]
		const worldOriginY = transform.translation[1]

		// 转换回地理坐标
		const mapCenterMercX = originMerc.x + worldOriginX * meterScale
		const mapCenterMercY = originMerc.y + worldOriginY * meterScale

		const mapCenterMerc = new mapboxgl.MercatorCoordinate(
			mapCenterMercX,
			mapCenterMercY
		)
		const mapCenterGeo = mapCenterMerc.toLngLat()

		return {
			mapCenter: [mapCenterGeo.lng, mapCenterGeo.lat],
			rotationDeg: (transform.rotation * 180) / Math.PI,
			scale: transform.scale,
			residualError: transform.residualError,
		}
	}
}




