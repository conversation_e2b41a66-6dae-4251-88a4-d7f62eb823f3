import * as THREE from "three"
import type { SunPosition } from "./TimeSystem"

/**
 * 渲染管理器
 * 负责管理不同频率的渲染更新，分离实时动画和环境效果
 */
export class RenderManager {
	private scene?: THREE.Scene
	private camera?: THREE.Camera
	private renderer?: THREE.WebGLRenderer
	private animationId: number | null = null
	private isRunning: boolean = false
	
	// 回调函数
	private renderCallbacks: Array<(camera: THREE.Camera) => void> = []
	private timeDisplayCallbacks: Array<(time: Date, sunPosition: SunPosition) => void> = []
	
	// 时间相关
	private lastTimeUpdate: number = 0
	private timeUpdateInterval: number = 1000 // 1秒更新一次时间显示

	constructor() {
		this.animate = this.animate.bind(this)
	}

	/**
	 * 设置Three.js渲染组件
	 */
	public setRenderComponents(scene: THREE.Scene, camera: THREE.Camera, renderer: THREE.WebGLRenderer) {
		this.scene = scene
		this.camera = camera
		this.renderer = renderer
	}

	/**
	 * 开始渲染循环
	 */
	public start(): void {
		if (!this.isRunning) {
			this.isRunning = true
			this.animate()
		}
	}

	/**
	 * 停止渲染循环
	 */
	public stop(): void {
		this.isRunning = false
		if (this.animationId) {
			cancelAnimationFrame(this.animationId)
			this.animationId = null
		}
	}

	/**
	 * 主渲染循环
	 * 只处理必要的渲染更新，避免频繁的全场景重绘
	 */
	private animate(): void {
		if (!this.isRunning) return

		const now = performance.now()

		// 执行渲染回调（如模型动画、水面效果等）
		if (this.camera) {
			this.renderCallbacks.forEach(callback => {
				try {
					callback(this.camera!)
				} catch (error) {
					console.warn("渲染回调执行失败:", error)
				}
			})
		}

		// 渲染Three.js场景
		if (this.scene && this.camera && this.renderer) {
			this.renderer.render(this.scene, this.camera)
		}

		// 继续动画循环
		this.animationId = requestAnimationFrame(this.animate)
	}

	/**
	 * 添加渲染回调
	 * 用于需要每帧更新的效果（如模型动画、水面等）
	 */
	public addRenderCallback(callback: (camera: THREE.Camera) => void): void {
		this.renderCallbacks.push(callback)
	}

	/**
	 * 移除渲染回调
	 */
	public removeRenderCallback(callback: (camera: THREE.Camera) => void): void {
		const index = this.renderCallbacks.indexOf(callback)
		if (index > -1) {
			this.renderCallbacks.splice(index, 1)
		}
	}

	/**
	 * 添加时间显示回调
	 * 用于UI时间显示更新（低频）
	 */
	public addTimeDisplayCallback(callback: (time: Date, sunPosition: SunPosition) => void): void {
		this.timeDisplayCallbacks.push(callback)
	}

	/**
	 * 移除时间显示回调
	 */
	public removeTimeDisplayCallback(callback: (time: Date, sunPosition: SunPosition) => void): void {
		const index = this.timeDisplayCallbacks.indexOf(callback)
		if (index > -1) {
			this.timeDisplayCallbacks.splice(index, 1)
		}
	}

	/**
	 * 触发时间显示更新
	 * 由TimeSystem调用
	 */
	public updateTimeDisplay(time: Date, sunPosition: SunPosition): void {
		const now = performance.now()
		if (now - this.lastTimeUpdate >= this.timeUpdateInterval) {
			this.lastTimeUpdate = now
			this.timeDisplayCallbacks.forEach(callback => {
				try {
					callback(time, sunPosition)
				} catch (error) {
					console.warn("时间显示回调执行失败:", error)
				}
			})
		}
	}

	/**
	 * 强制重新渲染一帧
	 * 用于环境变化时的立即更新
	 */
	public forceRender(): void {
		if (this.scene && this.camera && this.renderer) {
			this.renderer.render(this.scene, this.camera)
		}
	}

	/**
	 * 销毁渲染管理器
	 */
	public dispose(): void {
		this.stop()
		this.renderCallbacks.length = 0
		this.timeDisplayCallbacks.length = 0
		this.scene = undefined
		this.camera = undefined
		this.renderer = undefined
	}
}