# 重构后的MapApplication

这个目录包含了基于原始MapApplication重构后的简化版本，只保留了核心的基础功能。

## 文件结构

### 核心文件
- `MapApplication.ts` - 主要的地图应用类，包含基础功能
- `EventBus.ts` - 简单的事件总线实现
- `ModelTypes.ts` - 模型数据类型定义
- `ModelTreeManager.ts` - 模型树管理器实现
- `LayerManager.ts` - 基于数据类型的图层管理器
- `ModelDataAdapter.ts` - 数据适配器，处理JSON数据转换
- `Annotation3DTypes.ts` - 3D标注数据类型定义
- `Annotation3DManager.ts` - 3D标注管理器实现
- `LODTypes.ts` - LOD细节层次数据类型定义
- `DefaultLODManager.ts` - LOD管理器实现

### Vue组件
- `MapViewerRefactored.vue` - 重构后的主组件，使用子组件组合
- `App.vue` - 使用示例应用

### 子组件 (components/)
- `LoadingOverlay.vue` - 加载进度覆盖层
- `ModelList.vue` - 模型列表管理
- `BuildingControl.vue` - 建筑楼层控制
- `StatsPanel.vue` - 统计信息面板
- `ActionButtons.vue` - 批量操作按钮
- `ClickInfo.vue` - 点击信息显示
- `Annotation3DPanel.vue` - 3D标注管理面板
- `LODPanel.vue` - LOD细节层次控制面板
- `index.ts` - 组件导出索引

## 保留的功能

### 1. 基础配置
- Mapbox地图初始化
- 地图中心点、缩放等配置
- 湖北省博物馆坐标设置

### 2. Three.js图层初始化
- Three.js场景、相机、渲染器初始化
- 基础灯光设置（环境光和方向光）
- 自定义图层添加到Mapbox

### 3. 鼠标事件绑定
- 鼠标点击事件处理（raycast）
- 鼠标移动事件处理
- 事件回调注册和注销机制

### 4. 基础渲染
- Three.js场景渲染
- 坐标变换处理
- 强制重绘功能

## 移除的功能

- 图层管理器（LayerManager）
- 3D模型加载功能
- LOD管理器
- 导航路线功能
- 标注管理器
- 动画管理器
- 建筑物加载功能

## 使用方式

```typescript
import MapApplication from './MapApplication'

const app = new MapApplication({
  container: 'map-container',
  defaultCenter: [114.35962345673624, 30.564406727759334],
  defaultZoom: 18
})

// 等待地图加载完成
await app.mapLoadPromise

// 添加Three.js图层
app.addThreeJsLayer()

// 等待Three.js图层准备完成
await app.threeLayerReadyPromise

// 注册点击事件
app.addClickCallback((event) => {
  console.log('点击位置:', event.world)
})

// 注册鼠标移动事件
app.addMouseMoveCallback((event) => {
  console.log('鼠标位置:', event.world)
})
```

## 模型数据类型设计

### 核心特性

1. **扁平树结构** - 所有节点都是扁平存储，通过parentId建立层级关系
2. **预设根节点** - 场景根节点(scene_root)作为所有模型的顶级父节点
3. **楼层支持** - 室内建筑支持多楼层，便于楼层切换
4. **LOD优化** - 支持建筑外立面和室内的LOD切换

### 节点类型

- `SCENE` - 场景根节点
- `BUILDING_EXTERIOR` - 建筑外立面
- `BUILDING_FLOOR` - 建筑室内楼层
- `MODEL` - 普通模型
- `GROUP` - 模型组

### 数据结构示例

```typescript
// 建筑外立面（支持LOD）
const building = createBuildingExteriorNode(
  'main_building',
  '主展馆',
  { objPath: '/models/exterior.obj', mtlPath: '/models/exterior.mtl' },
  { x: 0, y: 0, z: 0 },
  true, // 有室内
  { switchDistance: 50, fadeDistance: 10, defaultFloor: 1 }
)

// 建筑楼层
const floor1 = createBuildingFloorNode(
  'main_building_floor_1',
  '主展馆一层',
  'main_building', // 父节点ID
  { objPath: '/models/floor_1.obj', mtlPath: '/models/floor_1.mtl' },
  1, // 楼层号
  '一层展厅',
  true // 默认显示
)
```

### LOD实现支持

- 建筑外立面和室内楼层分离
- 支持距离阈值和渐变距离配置
- 楼层可见性管理
- 默认楼层设置

## LayerManager功能

### 核心功能

1. **模型加载** - 支持OBJ/MTL格式模型加载
2. **批量操作** - 支持单个和批量模型加载
3. **建筑管理** - 专门的建筑加载（外立面+楼层）
4. **可见性控制** - 动态控制模型显示/隐藏
5. **资源管理** - 自动清理几何体和材质资源
6. **进度跟踪** - 详细的加载进度回调
7. **事件驱动** - 完整的事件通知机制

### 主要方法

```typescript
// 加载单个模型
await layerManager.loadNode('model_id', onProgress, onComplete)

// 批量加载模型
await layerManager.loadNodes(['id1', 'id2'], onProgress, onComplete)

// 加载建筑（外立面+楼层）
await layerManager.loadBuilding('building_id', onProgress)

// 控制可见性
layerManager.setNodeVisibility('model_id', false)

// 获取3D对象
const object3D = layerManager.getObject3D('model_id')

// 获取加载统计
const stats = layerManager.getLoadStats()
```

## Vue组件功能

### MapViewer.vue 特性

1. **完整集成** - 集成MapApplication、LayerManager和数据管理
2. **可视化界面** - 提供直观的模型管理界面
3. **实时控制** - 支持模型加载、可见性控制、楼层切换
4. **进度显示** - 详细的加载进度和状态显示
5. **交互功能** - 点击事件处理和信息显示
6. **响应式设计** - 适配不同屏幕尺寸

### 主要功能模块

- **加载界面** - 带进度条的启动加载界面
- **模型管理** - 模型列表、加载状态、可见性控制
- **建筑控制** - 楼层切换和建筑管理
- **统计信息** - 实时显示加载统计数据
- **批量操作** - 一键加载/隐藏所有模型
- **点击交互** - 显示3D场景中的点击位置

### 数据源

使用 `public/modelList.json` 作为模型数据源，通过 `ModelDataAdapter` 自动转换为内部数据格式。

### 使用方式

```vue
<template>
  <div id="app">
    <MapViewerRefactored />
  </div>
</template>

<script setup lang="ts">
import MapViewerRefactored from './refactored/MapViewerRefactored.vue'
</script>
```

## 3D标注功能

### 核心特性

1. **多种标注类型** - 支持点、文字、线、面四种标注类型
2. **3D空间定位** - 直接在三维场景中精确定位标注
3. **智能显示** - 支持距离淡化、始终置顶等显示策略
4. **分类管理** - 支持标注分类，便于组织管理
5. **样式自定义** - 每个标注可自定义颜色、大小、字体等样式
6. **数据导入导出** - 支持JSON格式和地理坐标格式的导入导出
7. **实时渲染** - 标注与3D场景实时同步渲染

### 标注类型

- **📍 点标注** - 在3D空间中标记重要位置
- **📝 文字标注** - 添加文字说明和描述信息（使用Sprite实现高性能渲染）
- **📏 线标注** - 绘制连接多个点的线条路径
- **🔷 面标注** - 标记封闭的区域范围，自动计算面积

### 主要功能

- **3D交互添加** - 点击3D场景中的位置添加标注
- **实时编辑** - 修改标注名称、文字内容、描述、分类等属性
- **可见性控制** - 显示/隐藏单个标注
- **分类筛选** - 按类别筛选显示标注
- **距离自适应** - 根据相机距离自动调整标注显示
- **导出JSON** - 将标注数据导出为JSON格式
- **导入JSON** - 从JSON文件导入标注数据
- **导出地理坐标** - 将标注数据导出为经纬度坐标格式
- **导入地理坐标** - 从地理坐标JSON文件导入标注数据
- **导出GeoJSON** - 将标注数据导出为标准GeoJSON格式，可直接在geojson.io等工具中使用
- **点击位置显示** - 显示点击位置的3D坐标和地理坐标（经纬度）

### 使用方式

1. **添加点标注** - 点击"📍 点"按钮，然后在3D场景中点击位置
2. **添加文字标注** - 点击"📝 文字"按钮，在3D场景中点击位置添加文字
3. **添加线标注** - 点击"📏 线"按钮，在3D场景中依次点击多个点绘制线条
4. **添加面标注** - 点击"🔷 面"按钮，在3D场景中依次点击多个点绘制封闭区域
5. **编辑标注** - 在标注列表中点击编辑按钮
6. **导出数据** - 点击"📤 导出JSON"下载数据文件
7. **导入数据** - 点击"📥 导入JSON"选择文件导入
8. **导出地理坐标** - 点击"🌍 导出地理坐标"下载经纬度格式数据
9. **导入地理坐标** - 点击"🌍 导入地理坐标"选择地理坐标文件导入
10. **导出GeoJSON** - 点击"📍 导出GeoJSON"下载标准GeoJSON格式文件
11. **查看点击坐标** - 点击3D场景任意位置，查看3D坐标和地理坐标

### 组件拆分优势

1. **模块化设计** - 每个功能独立成组件，便于维护
2. **可复用性** - 子组件可在其他项目中复用
3. **代码清晰** - 主组件逻辑简化，专注于数据管理
4. **易于测试** - 每个组件可独立测试
5. **性能优化** - 组件级别的更新优化
6. **功能完整** - 集成了3D模型管理和3D标注管理
7. **空间感知** - 3D标注与场景深度集成，提供真实的空间体验

### 组件接口

组件内部自动管理所有状态，无需外部props。所有功能通过UI界面操作：

- 点击"加载"按钮加载单个模型
- 点击"显示/隐藏"切换模型可见性
- 点击楼层按钮切换建筑楼层
- 使用批量操作按钮管理所有模型
- 点击地图查看3D坐标信息
- 使用3D标注工具在场景中添加各种标注
- 管理和编辑3D标注的属性和样式

## 集成方式

3D标注功能已完全集成到主MapViewer组件中，与3D模型管理并行工作，提供了完整的3D场景标注能力。用户可以在同一个界面中管理3D建筑模型和3D空间标注，实现丰富的三维空间数据可视化和交互体验。

### 技术特点

- **Sprite渲染** - 使用Three.js的Sprite实现高性能文字标注
- **空间定位** - 标注精确定位在3D世界坐标系中
- **相机同步** - 标注显示与相机位置实时同步
- **性能优化** - 距离裁剪和淡化减少渲染负担
- **事件集成** - 与现有的3D场景交互系统无缝集成
- **坐标转换** - 支持3D世界坐标与地理坐标（经纬度）的双向转换
- **点击交互** - 实时显示点击位置的坐标信息，支持复制和地图查看

## 特点

1. **简化架构** - 移除了复杂的管理器，只保留核心功能
2. **事件驱动** - 使用EventBus进行组件间通信
3. **数据驱动** - 完整的模型数据类型系统
4. **LOD友好** - 专门为LOD实现设计的数据结构
5. **Vue集成** - 完整的Vue组件，开箱即用
6. **用户友好** - 直观的UI界面和交互体验
7. **易于扩展** - 基础架构清晰，便于后续功能添加
8. **兼容性好** - 修复了Promise.withResolvers等兼容性问题

## 🌍 地理坐标导出功能

### 数据格式

地理坐标导出的JSON格式包含以下字段：

```json
{
  "type": "AnnotationCollection",
  "coordinateSystem": "WGS84",
  "exportTime": "2025-07-24T10:30:00.000Z",
  "converterConfig": {
    "mapCenter": {
      "longitude": 114.35962345673624,
      "latitude": 30.564406727759334,
      "altitude": 0
    }
  },
  "annotations": [
    {
      "id": "annotation_xxx",
      "name": "标注名称",
      "type": "point",
      "geoPosition": {
        "longitude": 114.359623,
        "latitude": 30.564407,
        "altitude": 10.5
      },
      "style": {...},
      "visible": true,
      "category": "info",
      "createdAt": "2025-07-24T10:30:00.000Z",
      "updatedAt": "2025-07-24T10:30:00.000Z"
    }
  ]
}
```

### 坐标系统

- **坐标系统**: WGS84 (World Geodetic System 1984)
- **经度范围**: -180° 到 +180°
- **纬度范围**: -90° 到 +90°
- **海拔单位**: 米（相对于海平面）

### 转换精度

- **精确转换器 (AccurateCoordinateConverter)** - 基于旧代码threeToLngLat实现
  - 使用Mapbox墨卡托坐标系统进行精确转换
  - 考虑模型旋转（26度）和地球曲率
  - 适用于大范围区域，转换精度极高
  - 完全兼容geojson.io等标准工具

- **简化转换器 (SimpleCoordinateConverter)** - 线性近似转换
  - 适用于小范围区域（如建筑群、园区）
  - 转换精度在100米范围内误差小于1厘米
  - 计算速度更快，适合实时应用

- 基准点设置为省博物馆坐标：114.35962345673624°E, 30.564406727759334°N

### 标准GeoJSON格式

标准GeoJSON导出格式完全符合RFC 7946规范，可直接在geojson.io、QGIS、ArcGIS等工具中使用：

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "id": "annotation_xxx",
      "geometry": {
        "type": "Point",
        "coordinates": [114.359623, 30.564407, 10.5]
      },
      "properties": {
        "name": "标注名称",
        "description": "标注描述",
        "annotationType": "point",
        "category": "info",
        "style": {...}
      }
    }
  ],
  "metadata": {
    "exportTime": "2025-07-24T10:30:00.000Z",
    "coordinateSystem": "WGS84",
    "source": "3D Annotation System"
  }
}
```

### 支持的几何类型

- **Point** - 点标注转换为GeoJSON Point
- **LineString** - 线标注转换为GeoJSON LineString
- **Polygon** - 面标注转换为GeoJSON Polygon（自动闭合）

现在用户可以在三维场景中直接添加各种类型的标注，并将标注数据导出为标准的地理坐标格式，便于在GIS系统和在线地图工具中使用！

## 📍 点击位置坐标显示

### 功能特性

- **双坐标系显示** - 同时显示3D世界坐标和地理坐标（经纬度）
- **实时转换** - 基于精确的Mapbox墨卡托坐标系统进行转换
- **一键复制** - 支持一键复制经纬度坐标到剪贴板
- **地图查看** - 直接在Google Maps中查看对应位置
- **响应式设计** - 适配桌面和移动设备

### 使用方式

1. **点击场景** - 在3D场景中点击任意位置
2. **查看坐标** - 左下角弹出坐标信息面板
3. **复制坐标** - 点击"📋 复制坐标"按钮复制经纬度
4. **地图查看** - 点击"🗺️ 在地图中查看"在Google Maps中打开
5. **关闭面板** - 点击"✕ 关闭"按钮关闭坐标面板

### 坐标信息

#### 3D坐标
- **X轴** - 东西方向位置（米）
- **Y轴** - 高度位置（米）
- **Z轴** - 南北方向位置（米）

#### 地理坐标
- **经度** - 东经坐标，精确到小数点后6位
- **纬度** - 北纬坐标，精确到小数点后6位
- **坐标系** - WGS84世界大地坐标系

### 技术实现

- **坐标转换** - 使用AccurateCoordinateConverter进行精确转换
- **模型旋转** - 考虑26度模型旋转角度
- **墨卡托投影** - 基于Mapbox墨卡托坐标系统
- **误差控制** - 转换精度达到厘米级别

## 🎮 LOD细节层次系统

### 核心特性

1. **智能距离控制** - 根据相机距离自动调整模型精度
2. **性能优化** - 显著提升大场景的渲染性能
3. **可配置参数** - 灵活的距离阈值和更新频率设置
4. **实时监控** - 详细的性能统计和级别分布显示
5. **开关控制** - 可随时启用或禁用LOD功能

### LOD级别

- **🔴 高精度 (High)** - 近距离显示完整细节模型
- **🟡 中精度 (Medium)** - 中等距离显示简化模型
- **🟢 低精度 (Low)** - 远距离显示低面数模型
- **⚫ 隐藏 (Hidden)** - 超远距离完全隐藏模型

### 主要功能

- **距离配置** - 自定义各级别的显示距离阈值
- **平滑过渡** - 支持级别切换的平滑动画
- **性能监控** - 实时显示模型数量、内存使用、帧率等
- **预加载功能** - 批量预加载所有级别的模型
- **级别分布** - 可视化显示当前各级别的模型数量

### 默认配置

```typescript
{
  enabled: true,
  distances: {
    high: 50,      // 50米内显示高精度
    medium: 200,   // 200米内显示中精度
    low: 500,      // 500米内显示低精度
    hidden: 1000   // 1000米外隐藏
  },
  smoothTransition: true,
  transitionDuration: 300,
  updateInterval: 100
}
```

### 使用方式

1. **启用/禁用LOD** - 使用顶部开关控制LOD功能
2. **调整距离** - 修改各级别的显示距离阈值
3. **配置选项** - 设置平滑过渡和更新频率
4. **监控性能** - 查看实时性能统计和级别分布
5. **预加载模型** - 点击"预加载所有"提前加载模型
6. **重置配置** - 点击"重置默认"恢复默认设置

### 性能优势

- **内存优化** - 根据距离动态加载/卸载模型
- **渲染优化** - 减少远距离模型的三角形数量
- **帧率提升** - 显著提高大场景的渲染帧率
- **加载优化** - 智能的模型加载优先级管理
