import * as THREE from "three"
import { NavigatorPathMeshGroup } from "./NavigatorPathMeshGroup"
import arrow from "@/assets/image/arrow.png"
import gsap from "gsap"
import { eventBus } from "./EventBus"
import pathUrl from "/path.json?url"
// const pathData = [
// 	{
// 		pathId: 1,
// 		points: [
// 			new THREE.Vector3(-21.130341994347983, -7, -4.85562779929063),
// 			new THREE.Vector3(-5, -7, 5),
// 			new THREE.Vector3(0, -7, 0),
// 			new THREE.Vector3(5, -7, 5),
// 			new THREE.Vector3(10, -7, 20),
// 			new THREE.Vector3(10, -7, 80),
// 		],
// 		buildingId: "",
// 		floor: -1,
// 	},
// 	{
// 		pathId: 2,
// 		points: [new THREE.Vector3(10, -7, 80)],
// 	},
// ]
const speed = 5

export class NavigationManager {
	private scene: THREE.Scene
	// private group?: NavigatorPathMeshGroup
	private originData: any
	private userPosition?: THREE.Vector3 = new THREE.Vector3(0, 0.1, 0)
	private userArrow?: THREE.Mesh
	private progress: number = 0
	private groupList: NavigatorPathMeshGroup[] = []
	private currentGroup?: NavigatorPathMeshGroup
	private totalDistance?: number
	private currentBuildingInfo: {
		buildingId: string
		floor: number
	} = {
			buildingId: "",
			floor: -1,
		}

	constructor(scene: THREE.Scene) {
		this.scene = scene
		const userGeometry = new THREE.CircleGeometry(1, 32)
		const userMaterial = new THREE.MeshBasicMaterial({
			transparent: true,
			map: new THREE.TextureLoader().load(arrow),
			side: THREE.DoubleSide,
			// depthTest: false,
		})
		this.userArrow = new THREE.Mesh(userGeometry, userMaterial)
		this.userArrow.rotateX(-Math.PI / 2)
		this.userArrow.position.set(1, 0.2, 0)
		this.userArrow.renderOrder = 999
		this.scene.add(this.userArrow)

		//! 测试代码
		this.testPathNavigation()
	}

	addPath(pathData: any) {
		// const points = pathData.points
		// const group = new NavigatorPathMeshGroup(points)

		// const { pos, dir } = group.getPointAndTangentAt(0)

		// this.userPosition = pos
		// this.userArrow?.position.copy(pos)
		// this.userArrow?.lookAt(pos.clone().add(dir))
		// this.userArrow?.rotateX(Math.PI / 2)

		// group.updateProgress(0.9)
		// this.scene.add(group)
		// this.group = group
		// this.previewNavigation()

		this.groupList = pathData.map((path: any) => {
			const group = new NavigatorPathMeshGroup(path.points)
			group.userData.pathId = path.pathId
			group.userData.buildingId = path.buildingId
			group.userData.floor = path.floor
			group.userData.progress = 0
			group.userData.distance = group.distance()
			const parent =
				this.scene.children.find(
					(child) =>
						child.name === `${path.buildingId}-floor${path.floor}`
				) || this.scene
			console.log(parent);

			parent.add(group)
			return group
		})
	}

	updateScale(camera: THREE.Camera) {
		//根据矩阵判断距离
		const position = this.getObserverPosition(camera)

		//根据距离判断缩放
		const distance = position.distanceTo(new THREE.Vector3(0, 0, 0))
		const ratio = Math.max(1, distance / 150)

		this.groupList?.forEach((group) => {
			group.updateWidth(ratio)
		})
		this.userArrow?.scale.set(ratio * 2, ratio * 2, ratio * 2)
	}

	updateDirection(angle) {
		this.userArrow?.rotation.set(-Math.PI / 2, 0, angle)
	}

	enterNavigateMode() {
		//todo 关闭LOD
		eventBus.emit("closeLOD")
		//todo 显示对应室内

		//todo 显示对应楼层室内热力

		//todo 添加路径

		this.addPath(this.originData)
		//todo 获取路径里程信息
		this.updateDistance()
	}

	previewNavigation() {
		gsap.to(this, {
			progress: 1,
			duration: this.totalDistance! / speed,
			onUpdate: () => {
				console.log(`当前进度: ${this.progress}`);

				this.updateProgress(this.progress)
			},
		})
	}

	private updateDistance() {
		this.progress = 0
		this.totalDistance = this.groupList.reduce((total, group) => {
			return group.userData.distance + total
		}, 0)
		this.groupList.forEach((group) => {
			group.userData.ratio = group.userData.distance / this.totalDistance!
		})
	}

	private updateProgress(totalProgress: number) {
		this.progress = totalProgress
		let accProgress = 0
		for (const group of this.groupList) {
			accProgress += group.userData.ratio
			if (accProgress >= this.progress) {
				this.currentGroup = group
				break
			}
		}
		const currentProgress = (this.progress - (accProgress - this.currentGroup!.userData.ratio)) / this.currentGroup!.userData.ratio
		this.currentGroup?.updateProgress(currentProgress)
		const { pos, dir } = this.currentGroup?.getPointAndTangentAt(
			currentProgress
		) || { pos: new THREE.Vector3(), dir: new THREE.Vector3() }
		pos.y += 0.1 // 确保用户位置在地面上方
		this.userPosition = pos
		this.userArrow?.position.copy(pos)
		this.userArrow?.lookAt(pos.clone().add(dir))
		this.userArrow?.rotateX(Math.PI / 2)

		//计算观察点坐标 与朝向的Y方向夹角45°
		// const distance = 40
		// const angle = Math.atan2(dir.x, dir.z) + Math.PI / 4 // 45度偏移
		// const observerX = pos.x + distance * Math.sin(angle)
		// const observerZ = pos.z + distance * Math.cos(angle)
		// const observerY = pos.y + 10 // 高度偏移

		// eventBus.emit('setCamera', this.userPosition, new THREE.Vector3(observerX, observerY, observerZ))
		eventBus.emit("repaint")
	}

	/**
	 * 获取真实的observer位置（适用于Mapbox环境）
	 */
	private getObserverPosition(camera: THREE.Camera): THREE.Vector3 {
		try {
			// 方法1：通过投影矩阵逆变换获取observer位置
			const camInverseProjection = camera.projectionMatrix
				.clone()
				.invert()
			const observerPosition = new THREE.Vector3().applyMatrix4(
				camInverseProjection
			)

			// 验证结果是否合理
			if (observerPosition.length() > 0 && isFinite(observerPosition.x)) {
				return observerPosition
			}
		} catch (error) {
			console.warn("⚠️ 投影矩阵逆变换失败:", error)
		}

		// 降级方案：使用相机position
		console.warn("⚠️ 使用降级方案：相机position")
		return camera.position.clone()
	}

	//! 测试
	private async testPathNavigation() {
		const pathData = await fetch(pathUrl)
		const json = await pathData.json()
		this.originData = json
		eventBus.on('loadComplete', () => {
			this.enterNavigateMode()
			this.previewNavigation()
		})
	}
}
