<template>
	<div class="map-viewer">
		<!-- 地图容器 -->
		<div ref="mapContainer" class="map-container"></div>

		<!-- 加载进度 -->
		<LoadingOverlay :visible="isLoading" :text="loadingText" :progress="loadingProgress" />

		<!-- 紧凑控制面板 -->
		<CompactControlPanel v-if="!isLoading" :models="modelList" :buildings="buildingList" :stats="stats"
			:lodManager="lodManager" @load-all="loadAllModels" @show-all="showAllModels" @hide-all="hideAllModels"
			@reset-view="resetView" @load-model="loadModel" @toggle-visibility="toggleVisibility"
			@switch-floor="switchFloor" />

		<!-- 点击信息显示 -->
		<ClickInfo :click-data="clickInfo" @close="clickInfo = null" />

		<!-- 室内建筑信息面板 -->
		<InteriorBuildingInfo :lodManager="lodManager" @floorChange="handleFloorChange"
			@configChange="handleLODConfigChange" />

		<!-- 3D标注面板 -->
		<Annotation3DPanel v-if="!isLoading && annotationManager" :annotationManager="annotationManager" />
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick, markRaw } from "vue"
import * as THREE from "three"
import MapApplication from "./MapApplication"
import { DefaultModelTreeManager } from "./ModelTreeManager"
import { ModelDataAdapter } from "./ModelDataAdapter"
import type { ModelNodeData } from "./ModelTypes"
import { eventBus } from "./EventBus"
import { DefaultAnnotation3DManager } from "./Annotation3DManager"
import type { Annotation3DManager } from "./Annotation3DTypes"
import { SimpleCoordinateConverter, AccurateCoordinateConverter } from "./MapboxCoordinateConverter"
import { SceneConfig } from "./SceneConfig"
import { DefaultLODManager } from "./DefaultLODManager"
import type { LODManager } from "./LODTypes"

// 导入组件
import LoadingOverlay from "./components/LoadingOverlay.vue"
import ClickInfo from "./components/ClickInfo.vue"
import InteriorBuildingInfo from "./components/InteriorBuildingInfo.vue"
import CompactControlPanel from "./components/CompactControlPanel.vue"
import Annotation3DPanel from "./components/Annotation3DPanel.vue"

// 响应式数据
const mapContainer = ref<HTMLElement>()
const isLoading = ref(true)
const loadingText = ref("初始化地图...")
const loadingProgress = ref(0)
const clickInfo = ref<{ x: number; y: number; z: number } | null>(null)

// MapApplication实例
let mapApp: MapApplication | null = null
let treeManager: DefaultModelTreeManager | null = null
const annotationManager = ref<Annotation3DManager | null>(null)
const lodManager = ref<LODManager | null>(null)

// 模型数据
const modelNodes = ref<ModelNodeData[]>([])

// 🔧 重新设计：使用普通响应式对象，确保Vue能正确追踪变化
const buildingCurrentFloors = ref<Record<string, number>>({})

// 计算属性
const modelList = computed(() =>
	ModelDataAdapter.getModelList(modelNodes.value)
)

const buildingList = computed(() => {
	console.log(`🔄 buildingList计算属性重新计算...`)
	console.log(`📊 当前楼层状态:`, buildingCurrentFloors.value)

	const buildings = ModelDataAdapter.getBuildingList(modelNodes.value)
	console.log(`🏗️ 原始建筑数据:`, buildings.length, '个建筑')

	// 🔧 简化逻辑：直接处理每个建筑的楼层选中状态
	const result = buildings.map(building => {
		// 提取建筑ID（去掉-exterior后缀）
		const actualBuildingId = building.id.replace('-exterior', '')
		const currentFloor = buildingCurrentFloors.value[actualBuildingId]

		console.log(`🔍 处理建筑 ${building.id} (实际ID: ${actualBuildingId}):`, {
			保存的楼层: currentFloor,
			原始楼层数: building.floors.length
		})

		// 为楼层添加选中状态
		const floorsWithSelection = building.floors.map(floor => ({
			...floor,
			isSelected: currentFloor === floor.floor
		}))

		console.log(`🎯 建筑 ${building.id} 最终状态:`, {
			currentFloor,
			选中的楼层: floorsWithSelection.filter(f => f.isSelected).map(f => f.floor)
		})

		return {
			...building,
			floors: floorsWithSelection,
			currentFloor: currentFloor
		}
	})

	console.log(`✅ buildingList计算完成，返回 ${result.length} 个建筑`)
	return result
})

const stats = computed(() => ModelDataAdapter.getStats(modelNodes.value))

// 设置事件监听器
const setupEventListeners = () => {
	eventBus.on("model-loaded", (data) => {
		console.log(`✅ 模型加载成功: ${data.nodeData.name}`)
		updateModelNodes()
		mapApp?.repaint()
	})

	eventBus.on("model-load-error", (data) => {
		console.error(`❌ 模型加载失败: ${data.nodeData.name}`)
		updateModelNodes()
	})

	eventBus.on("model-load-progress", (data) => {
		console.log(
			`📊 ${data.nodeId} 加载进度: ${Math.round(data.progress * 100)}%`
		)
	})
}

// 加载模型数据
const loadModelData = async () => {
	try {
		loadingText.value = "加载模型数据..."
		loadingProgress.value = 20

		const nodes = await ModelDataAdapter.loadFromJSON()

		if (!treeManager) return

		// 添加节点到树管理器
		nodes.forEach((node) => {
			treeManager!.addNode(node)
		})

		updateModelNodes()
		console.log(`✅ 加载了 ${nodes.length} 个模型节点`)
	} catch (error) {
		console.error("❌ 加载模型数据失败:", error)
	}
}

// 更新模型节点数据
const updateModelNodes = () => {
	if (!treeManager) return

	// 🔧 修复：保持现有的UI状态，只更新必要的数据
	const newNodes = treeManager.getAllNodes()

	// 如果是首次加载，直接设置
	if (modelNodes.value.length === 0) {
		modelNodes.value = newNodes
		return
	}

	// 否则，智能合并状态，保持UI选中状态
	const existingNodesMap = new Map(modelNodes.value.map(node => [node.id, node]))

	modelNodes.value = newNodes.map(newNode => {
		const existingNode = existingNodesMap.get(newNode.id)
		if (existingNode) {
			// 保持现有节点的UI状态，只更新必要的属性
			return {
				...newNode,
				// 保持UI相关的状态（如果有的话）
				// 这里可以根据需要保持特定的UI状态
			}
		}
		return newNode
	})

	console.log(`🔄 智能更新模型节点: ${newNodes.length} 个节点，保持UI状态`)
}

// 初始化应用
const initializeApp = async () => {
	if (!mapContainer.value) return

	try {
		loadingText.value = "初始化地图应用..."
		loadingProgress.value = 10

		// 创建MapApplication实例
		mapApp = new MapApplication({
			container: mapContainer.value,
			defaultCenter: [114.35962345673624, 30.564406727759334],
			defaultZoom: 18,
		})

		treeManager = mapApp.getTreeManager() as DefaultModelTreeManager

		loadingText.value = "等待地图加载..."
		loadingProgress.value = 30

		// 等待地图加载完成
		await mapApp.mapLoadPromise

		loadingText.value = "初始化3D图层..."
		loadingProgress.value = 60

		// 添加Three.js图层
		mapApp.addThreeJsLayer()

		// 等待Three.js图层准备完成
		await mapApp.threeLayerReadyPromise

		// 初始化3D标注管理器
		if (mapApp.scene && mapApp.camera && mapApp.renderer && mapContainer.value) {
			console.log("test");

			annotationManager.value = new DefaultAnnotation3DManager(
				mapApp.scene,
				mapApp.camera,
				mapApp.renderer,
				mapContainer.value
			)

			// 设置坐标转换器 - 使用精确的Mapbox转换器
			// 🔧 统一：使用场景配置中的统一参数
			const coordinateConverter = new AccurateCoordinateConverter(
				SceneConfig.getMapCenter(), // 统一的地图中心
				SceneConfig.getModelRotation(), // 统一的模型旋转
				SceneConfig.getBaseAltitude() // 统一的基准海拔
			)
			annotationManager.value.setCoordinateConverter(coordinateConverter)
		}

		// 初始化LOD管理器
		if (mapApp.scene) {
			lodManager.value = markRaw(new DefaultLODManager(mapApp.scene))

			// 🔧 连接LayerManager到LOD系统
			const layerManager = mapApp.getLayerManager()
			if (layerManager && typeof (lodManager.value as any).setLayerManager === 'function') {
				(lodManager.value as any).setLayerManager(layerManager)
			}

			console.log('✅ LOD管理器已初始化')

			// 添加LOD事件监听
			lodManager.value.on('level-changed', (data: any) => {
				console.log('🔄 LOD级别变化:', data)
			})

			lodManager.value.on('model-loaded', (data: any) => {
				console.log('📦 LOD模型加载:', data)
			})

			// 🔧 新增：监听渲染更新事件
			lodManager.value.on('render-update-required', (data: any) => {
				console.log('🎬 LOD要求渲染更新:', data)
				if (mapApp) {
					mapApp.repaint()
				}
			})
		}

		// 监听LayerManager的模型加载事件，自动注册到LOD
		eventBus.on('model-loaded', (data: any) => {
			const { nodeId, object3D } = data
			if (lodManager && object3D) {
				registerModelToLOD(nodeId, object3D)
			}
		})

		// 加载模型数据
		await loadModelData()

		loadingText.value = "设置事件监听..."
		loadingProgress.value = 90

		// 设置点击事件
		mapApp.addClickCallback((event) => {
			if (event.world) {
				clickInfo.value = {
					x: event.world.x,
					y: event.world.y,
					z: event.world.z,
				}

				// 处理3D标注的点击交互
				if (annotationManager.value && mapApp?.camera) {
					annotationManager.value.handleClick(event.world, mapApp.camera)
				}
			}
		})

		// 设置渲染回调，用于更新3D标注和LOD
		mapApp.addRenderCallback((camera) => {
			if (annotationManager.value) {
				annotationManager.value.updateCameraPosition(camera)
			}
			if (lodManager.value) {
				lodManager.value.updateLOD(camera)
			}
		})



		loadingProgress.value = 100

		// 延迟隐藏加载界面
		setTimeout(() => {
			isLoading.value = false
		}, 500)

		console.log("✅ MapApplication初始化完成")
	} catch (error) {
		console.error("❌ 初始化失败:", error)
		loadingText.value = "初始化失败"
	}
}

// 加载单个模型
const loadModel = async (nodeId: string) => {
	if (!mapApp) return
	const layerManager = mapApp.getLayerManager()
	if (!layerManager || !treeManager) return

	try {
		console.log(`🔄 开始加载单个模型: ${nodeId}`)

		// 使用LayerManager加载模型，并在完成时注册LOD
		await layerManager.loadNode(
			nodeId,
			(nodeId: string, progress: number) => {
				// 进度回调
				console.log(`📊 模型 ${nodeId} 加载进度: ${(progress * 100).toFixed(1)}%`)
			},
			(nodeId: string, object3D: THREE.Object3D | null, success: boolean, errorMessage?: string) => {
				// 模型加载完成回调 - 在这里注册LOD
				if (success && object3D && lodManager.value) {
					console.log(`✅ 模型 ${nodeId} 加载成功，开始注册LOD`)
					registerModelToLOD(nodeId, object3D)
				} else {
					console.error(`❌ 模型 ${nodeId} 加载失败:`, errorMessage || '未知错误')
				}
			}
		)
		updateModelNodes()
		console.log(`🎯 模型 ${nodeId} 加载流程完成`)
	} catch (error) {
		console.error(`❌ 加载模型 ${nodeId} 失败:`, error)
	}
}

// 将模型注册到LOD系统
const registerModelToLOD = (nodeId: string, object3D: THREE.Object3D) => {
	if (!lodManager || !treeManager) return

	const node = treeManager.getNodeById(nodeId)
	if (!node) return

	// 计算真实的世界位置（参考旧版LOD实现）
	let realPosition = new THREE.Vector3()



	// 使用第一个子网格的bounding sphere center
	if (object3D.children.length > 0) {
		const firstChild = object3D.children[0] as THREE.Mesh
		if (firstChild.position.length() !== 0) {
			realPosition = firstChild.position.clone()
		} else if (firstChild?.geometry) {
			if (!firstChild.geometry.boundingSphere) {
				firstChild.geometry.computeBoundingSphere()
			}
			if (firstChild.geometry.boundingSphere) {
				realPosition = firstChild.geometry.boundingSphere.center.clone()
			}
		}
	}

	// 如果没有bounding sphere，使用包围盒中心
	if (realPosition.length() === 0) {
		const box = new THREE.Box3().setFromObject(object3D)
		if (!box.isEmpty()) {
			box.getCenter(realPosition)
		}
	}

	// 🔧 修复：根据节点类型设置不同的LOD行为
	let lodConfig: any = {
		id: nodeId,
		name: nodeId,
		position: realPosition,
		object: object3D,
		priority: 0,
		hasParent: !!node.parentId, // 添加parentId信息
	}

	// 🔧 修复：建筑级LOD组织
	if (node.type === 'building_floor') {
		const floorNode = node as any
		// 从nodeId中提取建筑ID（假设格式为 BuildingName-floor1）
		const buildingId = nodeId.split('-')[0]

		lodConfig.isInterior = true
		lodConfig.buildingId = buildingId  // 使用提取的建筑ID
		lodConfig.floor = floorNode.floor || 1
		lodConfig.isDefault = floorNode.isDefault || (floorNode.floor === 1)

		// 室内模型默认隐藏，只有在近距离时才显示
		object3D.visible = false
		console.log(`🏢 室内模型注册: ${nodeId}, 建筑ID: ${buildingId}, 楼层: ${lodConfig.floor}, 默认显示: ${lodConfig.isDefault}`)
	} else if (node.type === 'building_exterior') {
		const exteriorNode = node as any
		// 外立面的建筑ID就是自己的ID（或者去掉-exterior后缀）
		const buildingId = nodeId.replace('-exterior', '')

		// 🔧 修复：检查是否真的有室内模型
		const hasInterior = exteriorNode.hasInterior || false

		lodConfig.isExterior = true
		lodConfig.buildingId = buildingId
		lodConfig.hasInterior = hasInterior
		lodConfig.switchDistance = exteriorNode.lodConfig?.switchDistance || 50
		lodConfig.fadeDistance = exteriorNode.lodConfig?.fadeDistance || 10

		console.log(`🏛️ 外立面模型注册: ${nodeId}, 建筑ID: ${buildingId}, 有室内: ${hasInterior}`)
	} else {
		// 普通模型 - 不参与LOD
		console.log(`📦 普通模型注册: ${nodeId}, 不参与LOD系统`)
	}

	// 注册到LOD系统
	if (lodManager.value && typeof (lodManager.value as any).addModelWithObject === 'function') {
		(lodManager.value as any).addModelWithObject(lodConfig)

		// 输出LOD注册状态
		const totalModels = lodManager.value?.getAllModels().length || 0
		console.log(`✅ LOD注册成功: ${nodeId}, 类型: ${node.type}, 位置: ${realPosition.x.toFixed(1)}, ${realPosition.y.toFixed(1)}, ${realPosition.z.toFixed(1)}, 总模型数: ${totalModels}`)

		// 🔧 调试：输出LOD配置详情
		if (node.type === 'building_exterior') {
			console.log(`🔍 外立面LOD配置详情:`, {
				nodeId,
				isExterior: lodConfig.isExterior,
				hasInterior: lodConfig.hasInterior,
				buildingId: lodConfig.buildingId,
				switchDistance: lodConfig.switchDistance,
				fadeDistance: lodConfig.fadeDistance,
				originalHasInterior: (node as any).hasInterior
			})
		}

		// 🔧 修复：恢复UI状态 - 当外立面注册时，检查是否有保存的楼层状态
		if (node.type === 'building_exterior' && lodConfig.hasInterior) {
			const buildingId = lodConfig.buildingId
			const savedFloor = (treeManager as any).getBuildingCurrentFloor?.(buildingId)

			if (savedFloor) {
				console.log(`🔄 恢复建筑 ${buildingId} 的UI状态: 楼层 ${savedFloor}`)
				// 同步LOD系统状态
				if (lodManager.value && typeof (lodManager.value as any).setCurrentFloor === 'function') {
					(lodManager.value as any).setCurrentFloor(buildingId, savedFloor)
				}
			}
		}
	}
}

// 切换可见性
const toggleVisibility = (nodeId: string) => {
	if (!mapApp) return
	const layerManager = mapApp.getLayerManager()
	if (!layerManager || !treeManager) return

	const node = treeManager.getNodeById(nodeId)
	if (!node) {
		console.error(`节点 ${nodeId} 未找到`)
		return
	}

	const oldVisibility = node.visible
	const newVisibility = !oldVisibility

	console.log(
		`切换 ${node.name} 可见性: ${oldVisibility} -> ${newVisibility}`
	)

	layerManager.setNodeVisibility(nodeId, newVisibility)
	updateModelNodes()
	mapApp.repaint()

	// 验证更新后的状态
	const updatedNode = treeManager.getNodeById(nodeId)
	console.log(`更新后状态: ${updatedNode?.visible}`)
}

// 切换楼层
const switchFloor = (buildingId: string, targetFloor: number) => {
	if (!mapApp || !treeManager) return
	const layerManager = mapApp.getLayerManager()
	if (!layerManager) return

	console.log(`🎮 UI切换楼层: ${buildingId} → 楼层 ${targetFloor}`)

	// 🔧 简化逻辑：直接设置楼层状态
	console.log(`🎮 UI切换楼层: ${buildingId} → 楼层 ${targetFloor}`)

	// 1. 直接保存UI状态（这会自动触发buildingList计算属性更新）
	buildingCurrentFloors.value[buildingId] = targetFloor
	console.log(`💾 保存UI状态: ${buildingId} → 楼层 ${targetFloor}`)
	console.log(`📊 当前所有楼层状态:`, buildingCurrentFloors.value)

	// 2. 更新数据模型
	if (treeManager) {
		treeManager.setBuildingCurrentFloor(buildingId, targetFloor)
	}

	// 3. 通过LOD系统控制楼层显示
	if (lodManager.value && typeof (lodManager.value as any).setCurrentFloor === 'function') {
		(lodManager.value as any).setCurrentFloor(buildingId, targetFloor)
		console.log(`✅ 楼层切换通过LOD系统完成`)
	}

	// 4. 验证更新是否生效
	setTimeout(() => {
		const building = buildingList.value.find(b => b.id.includes(buildingId))
		if (building) {
			const selectedFloors = building.floors.filter(f => f.isSelected)
			console.log(`✅ UI更新验证:`, {
				建筑: building.id,
				当前楼层: building.currentFloor,
				选中楼层: selectedFloors.map(f => f.floor)
			})
		}
	}, 100)

	mapApp.repaint()
}

// 加载所有模型
const loadAllModels = async () => {
	if (!mapApp) return
	const layerManager = mapApp.getLayerManager()
	if (!layerManager || !treeManager) return

	const allNodes = treeManager
		.getAllNodes()
		.filter((node) => node.loadState === "pending")

	for (const node of allNodes) {
		try {
			await layerManager.loadNode(node.id)
		} catch (error) {
			console.error(`加载 ${node.name} 失败:`, error)
		}
	}

	updateModelNodes()
}

// 隐藏所有模型
const hideAllModels = () => {
	if (!mapApp || !treeManager) return
	const layerManager = mapApp.getLayerManager()
	if (!layerManager) return

	const allNodes = treeManager
		.getAllNodes()
		.filter((node) => node.type !== "scene")

	allNodes.forEach((node) => {
		if (node.visible) {
			layerManager.setNodeVisibility(node.id, false)
		}
	})

	updateModelNodes()
	mapApp.repaint()
}

// 显示所有模型
const showAllModels = () => {
	if (!mapApp || !treeManager) return
	const layerManager = mapApp.getLayerManager()
	if (!layerManager) return

	const allNodes = treeManager
		.getAllNodes()
		.filter((node) => node.type !== "scene")

	allNodes.forEach((node) => {
		if (!node.visible) {
			layerManager.setNodeVisibility(node.id, true)
		}
	})

	updateModelNodes()
	mapApp.repaint()
}

// 重置视图
const resetView = () => {
	if (!mapApp) return
	mapApp.getMap().flyTo({
		center: [114.35962345673624, 30.564406727759334],
		zoom: 18,
		pitch: 0,
		bearing: 0,
	})
}


// 生命周期
onMounted(async () => {
	setupEventListeners()
	await nextTick()
	await initializeApp()
})

// 🔧 新增：处理楼层切换
const handleFloorChange = (buildingId: string, floor: number) => {
	console.log(`🏢 楼层切换请求: ${buildingId} → 楼层 ${floor}`)

	if (lodManager.value) {
		// 设置LOD管理器中的楼层
		if (typeof lodManager.value.setCurrentFloor === 'function') {
			lodManager.value.setCurrentFloor(buildingId, floor)
		}

		// 🔧 修复：使用mapApp.camera而不是camera
		if (mapApp?.camera && typeof lodManager.value.updateLOD === 'function') {
			lodManager.value.updateLOD(mapApp.camera)
			console.log(`🔄 楼层切换后触发LOD更新: ${buildingId} → 楼层 ${floor}`)
		} else {
			console.warn('⚠️ 无法触发LOD更新: mapApp.camera 不可用')
		}
	}
}

// 🔧 新增：处理LOD配置变更
const handleLODConfigChange = (config: { switchDistance?: number; fadeDistance?: number }) => {
	console.log(`🔧 LOD配置变更:`, config)

	if (lodManager.value && typeof lodManager.value.setDefaultLODConfig === 'function') {
		lodManager.value.setDefaultLODConfig(config)
	}
}

// 🔧 LOD调试工具
const diagnoseLODState = () => {
	console.log('🔍 Vue组件：开始LOD状态诊断...')
	console.log('📊 LOD管理器状态:', {
		exists: !!lodManager.value,
		type: typeof lodManager.value,
		methods: lodManager.value ? Object.getOwnPropertyNames(Object.getPrototypeOf(lodManager.value)) : []
	})

	if (lodManager.value && typeof (lodManager.value as any).diagnoseLODState === 'function') {
		console.log('✅ 调用LOD诊断方法...')
			; (lodManager.value as any).diagnoseLODState()
	} else {
		console.warn('⚠️ LOD管理器不可用或不支持诊断功能')
		console.warn('🔍 可用方法:', lodManager.value ? Object.getOwnPropertyNames(Object.getPrototypeOf(lodManager.value)) : 'LOD管理器不存在')
	}
}

const fixLODStates = () => {
	console.log('🔧 Vue组件：开始修复LOD状态...')

	if (lodManager.value && typeof (lodManager.value as any).fixAllModelStates === 'function') {
		console.log('✅ 调用LOD修复方法...')
			; (lodManager.value as any).fixAllModelStates()

		// 强制重新渲染
		if (mapApp) {
			mapApp.repaint()
		}
	} else {
		console.warn('⚠️ LOD管理器不可用或不支持修复功能')
		console.warn('🔍 可用方法:', lodManager.value ? Object.getOwnPropertyNames(Object.getPrototypeOf(lodManager.value)) : 'LOD管理器不存在')
	}
}

onUnmounted(() => {
	lodManager.value?.dispose()
	annotationManager.value?.dispose()
	mapApp?.destroy()
})
</script>

<style scoped>
.map-viewer {
	position: relative;
	width: 100%;
	height: 100vh;
	overflow: hidden;
}

.map-container {
	width: 100%;
	height: 100%;
}
</style>
