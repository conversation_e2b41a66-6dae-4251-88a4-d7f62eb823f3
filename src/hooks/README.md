# useRefactoredMapViewer Hook

这个 hook 封装了 refactored 目录下的各类 UI 使用的接口，为 PageIndex 和其他组件提供统一的地图查看器功能。

## 功能特性

### 核心功能
- **地图初始化**: 基于 Mapbox + Three.js 的 3D 地图应用
- **智能进度计算**: 分阶段计算 Mapbox + Three.js + 所有建筑物的整体加载进度
- **自动模型加载**: Three.js 图层加载完成后立即开始加载建筑地形等模型
- **模型管理**: 支持 3D 模型的加载、显示/隐藏、批量操作
- **楼层控制**: 建筑物楼层切换和室内导航
- **LOD 管理**: 细节层次管理，优化性能
- **3D 标注**: 3D 场景中的标注管理
- **点击交互**: 地图点击事件处理

### UI 组件集成
- **LoadingOverlay**: 加载进度显示
- **CompactControlPanel**: 紧凑控制面板
- **ClickInfo**: 点击信息显示
- **InteriorBuildingInfo**: 室内建筑信息面板
- **Annotation3DPanel**: 3D 标注管理面板

## 使用方法

### 基本用法

```vue
<template>
  <div class="map-viewer">
    <!-- 地图容器 -->
    <div ref="mapContainer" class="map-container"></div>

    <!-- 加载进度 -->
    <LoadingOverlay :visible="isLoading" :text="loadingText" :progress="loadingProgress" />

    <!-- 控制面板 -->
    <CompactControlPanel 
      v-if="!isLoading" 
      :models="modelList" 
      :buildings="buildingList" 
      :stats="stats"
      :lodManager="lodManager" 
      @load-all="loadAllModels" 
      @show-all="showAllModels" 
      @hide-all="hideAllModels"
      @reset-view="resetView" 
      @load-model="loadModel" 
      @toggle-visibility="toggleVisibility"
      @switch-floor="switchFloor" 
    />

    <!-- 点击信息 -->
    <ClickInfo :click-data="clickInfo" @close="closeClickInfo" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRefactoredMapViewer } from '@/hooks/useRefactoredMapViewer'

// 导入组件
import LoadingOverlay from '@/refactored/components/LoadingOverlay.vue'
import CompactControlPanel from '@/refactored/components/CompactControlPanel.vue'
import ClickInfo from '@/refactored/components/ClickInfo.vue'

const mapContainer = ref<HTMLElement>()

// 使用 hook
const {
  // 响应式状态
  isLoading,
  loadingText,
  loadingProgress,
  clickInfo,
  
  // 计算属性
  modelList,
  buildingList,
  stats,
  
  // 管理器实例
  lodManager,
  
  // 方法
  loadModel,
  loadAllModels,
  toggleVisibility,
  hideAllModels,
  showAllModels,
  switchFloor,
  resetView,
  closeClickInfo,
  initializeApp,
  setupEventListeners,
  dispose
} = useRefactoredMapViewer({
  defaultCenter: [114.35962345673624, 30.564406727759334],
  defaultZoom: 18,
})

// 生命周期
onMounted(async () => {
  setupEventListeners()
  await nextTick()
  
  if (mapContainer.value) {
    await initializeApp(mapContainer.value)
  }
})

onUnmounted(() => {
  dispose()
})
</script>
```

## API 参考

### 配置选项

```typescript
interface RefactoredMapViewerConfig {
  container?: HTMLElement        // 地图容器元素（可选，可在初始化时传入）
  defaultCenter?: [number, number]  // 默认地图中心点
  defaultZoom?: number          // 默认缩放级别
}
```

### 返回值

#### 响应式状态
- `isLoading`: 是否正在加载
- `loadingText`: 加载文本
- `loadingProgress`: 加载进度 (0-100)
- `clickInfo`: 点击信息数据

#### 计算属性
- `modelList`: 模型列表
- `buildingList`: 建筑列表（包含楼层信息）
- `stats`: 统计信息

#### 管理器实例
- `annotationManager`: 3D 标注管理器
- `lodManager`: LOD 管理器

#### 方法
- `initializeApp(container?: HTMLElement)`: 初始化应用
- `setupEventListeners()`: 设置事件监听器
- `loadModel(nodeId: string)`: 加载单个模型
- `loadAllModels()`: 加载所有模型
- `toggleVisibility(nodeId: string)`: 切换模型可见性
- `hideAllModels()`: 隐藏所有模型
- `showAllModels()`: 显示所有模型
- `switchFloor(buildingId: string, floor: number)`: 切换楼层
- `handleFloorChange(buildingId: string, floor: number)`: 处理楼层变化
- `resetView()`: 重置视图
- `handleLODConfigChange(config)`: 处理 LOD 配置变更
- `closeClickInfo()`: 关闭点击信息
- `dispose()`: 清理资源

## 进度计算机制

Hook 采用分阶段的进度计算方式，确保用户能够准确了解加载状态：

### 进度阶段分配
- **Mapbox 地图加载**: 20%
- **Three.js 图层初始化**: 20%
- **模型数据加载**: 10%
- **所有建筑模型加载**: 50%

### 模型加载策略
1. **自动加载**: Three.js 图层准备完成后立即开始加载所有模型
2. **批量处理**: 每批最多并行加载 3 个模型，避免资源竞争
3. **实时进度**: 每个模型的加载进度实时反映到整体进度中
4. **错误处理**: 加载失败的模型也会标记为完成，确保进度正常推进

### 进度计算公式
```
整体进度 = Mapbox进度(20%) + Three.js进度(20%) + 模型数据进度(10%) + 模型加载进度(50%)

模型加载进度 = (已完成模型数 / 总模型数) × 50%
```

## 注意事项

1. **容器元素**: 确保在调用 `initializeApp` 时传入有效的 DOM 元素
2. **生命周期**: 在组件卸载时调用 `dispose()` 方法清理资源
3. **事件监听**: 在组件挂载时调用 `setupEventListeners()` 设置事件监听
4. **异步初始化**: `initializeApp` 是异步方法，需要等待完成
5. **进度监控**: 进度计算是自动的，无需手动干预

## 示例项目

参考 `src/page/index.vue` 文件查看完整的使用示例。
