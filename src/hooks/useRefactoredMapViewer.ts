import { ref, computed, markRaw } from "vue"
import * as THREE from "three"
import MapApplication from "@/refactored/MapApplication"
import { DefaultModelTreeManager } from "@/refactored/ModelTreeManager"
import { ModelDataAdapter } from "@/refactored/ModelDataAdapter"
import type { ModelNodeData } from "@/refactored/ModelTypes"
import { eventBus } from "@/refactored/EventBus"
import { DefaultAnnotation3DManager } from "@/refactored/Annotation3DManager"
import type { Annotation3DManager } from "@/refactored/Annotation3DTypes"
import { AccurateCoordinateConverter } from "@/refactored/MapboxCoordinateConverter"
import { SceneConfig } from "@/refactored/SceneConfig"
import { DefaultLODManager } from "@/refactored/DefaultLODManager"
import type { LODManager } from "@/refactored/LODTypes"

export interface RefactoredMapViewerConfig {
	container?: HTMLElement
	defaultCenter?: [number, number]
	defaultZoom?: number
}

export interface ClickData {
	x: number
	y: number
	z: number
}

/**
 * 封装 refactored 各类 UI 使用的接口为一个 hook
 * 供给 PageIndex 使用
 */
export function useRefactoredMapViewer(config: RefactoredMapViewerConfig) {
	// 响应式数据
	const isLoading = ref(true)
	const loadingText = ref("初始化地图...")
	const loadingProgress = ref(0)
	const clickInfo = ref<ClickData | null>(null)

	// 核心实例
	let mapApp: MapApplication | null = null
	let treeManager: DefaultModelTreeManager | null = null
	const annotationManager = ref<Annotation3DManager | null>(null)
	const lodManager = ref<LODManager | null>(null)

	// 模型数据
	const modelNodes = ref<ModelNodeData[]>([])
	const buildingCurrentFloors = ref<Record<string, number>>({})

	// 进度计算相关
	const progressStages = {
		mapbox: { weight: 20, completed: false }, // Mapbox 地图加载 20%
		threejs: { weight: 20, completed: false }, // Three.js 图层初始化 20%
		modelData: { weight: 10, completed: false }, // 模型数据加载 10%
		models: { weight: 50, completed: false }, // 所有模型加载 50%
	}

	const modelLoadProgress = ref<Record<string, number>>({}) // 每个模型的加载进度
	let totalModelsCount = 0 // 总模型数量

	// 计算整体进度
	const calculateOverallProgress = () => {
		let totalProgress = 0

		// 计算各阶段进度
		Object.entries(progressStages).forEach(([stage, config]) => {
			if (config.completed) {
				totalProgress += config.weight
			} else if (stage === "models" && totalModelsCount > 0) {
				// 模型加载阶段：根据各个模型的加载进度计算
				const modelProgressValues = Object.values(
					modelLoadProgress.value
				)
				const averageModelProgress =
					modelProgressValues.length > 0
						? modelProgressValues.reduce(
							(sum, progress) => sum + progress,
							0
						) / totalModelsCount
						: 0
				totalProgress += config.weight * averageModelProgress
			}
		})

		loadingProgress.value = Math.min(Math.round(totalProgress), 100)
		// console.log(`📊 整体进度: ${loadingProgress.value}%`, {
		// 	stages: progressStages,
		// 	modelProgress: modelLoadProgress.value,
		// 	totalModels: totalModelsCount,
		// })
	}

	// 标记阶段完成
	const markStageCompleted = (stage: keyof typeof progressStages) => {
		progressStages[stage].completed = true
		calculateOverallProgress()
		console.log(`✅ 阶段完成: ${stage}`)
	}

	// 更新模型加载进度
	const updateModelProgress = (nodeId: string, progress: number) => {
		modelLoadProgress.value[nodeId] = progress
		calculateOverallProgress()
	}

	// 计算属性
	const modelList = computed(() =>
		ModelDataAdapter.getModelList(modelNodes.value)
	)

	const buildingList = computed(() => {
		const buildings = ModelDataAdapter.getBuildingList(modelNodes.value)

		return buildings.map((building) => {
			const actualBuildingId = building.id.replace("-exterior", "")
			const currentFloor = buildingCurrentFloors.value[actualBuildingId]

			const floorsWithSelection = building.floors.map((floor) => ({
				...floor,
				isSelected: currentFloor === floor.floor,
			}))

			return {
				...building,
				floors: floorsWithSelection,
				currentFloor: currentFloor,
			}
		})
	})

	const stats = computed(() => ModelDataAdapter.getStats(modelNodes.value))

	// 检查是否所有模型都已加载完成
	const checkAllModelsLoaded = () => {
		if (totalModelsCount === 0) return false

		const completedModels = Object.values(modelLoadProgress.value).filter(
			(progress) => progress >= 1.0
		).length
		const allCompleted = completedModels >= totalModelsCount

		if (allCompleted && !progressStages.models.completed) {
			console.log(`🎉 所有 ${totalModelsCount} 个模型加载完成!`)
			markStageCompleted("models")

			// 🔧 重新启用LOD更新
			if (lodManager.value) {
				console.log("✅ 重新启用LOD更新")
				lodManager.value.setEnabled(true)

				// 立即执行一次LOD更新以确保状态正确
				if (
					mapApp?.camera &&
					typeof (lodManager.value as any).forceUpdateLOD ===
					"function"
				) {
					; (lodManager.value as any).forceUpdateLOD(mapApp.camera)
				}
			}

			// 延迟隐藏加载界面
			setTimeout(() => {
				isLoading.value = false
				eventBus.emit("repaint")
			}, 500)
		}

		return allCompleted
	}

	// 设置事件监听器
	const setupEventListeners = () => {
		eventBus.on("model-loaded", (data) => {
			// console.log(`✅ 模型加载成功: ${data.nodeData.name}`)
			// 标记该模型加载完成
			updateModelProgress(data.nodeData.id, 1.0)
			updateModelNodes()
			mapApp?.repaint()

			// 检查是否所有模型都已加载完成
			checkAllModelsLoaded()
		})

		eventBus.on("model-load-error", (data) => {
			console.error(`❌ 模型加载失败: ${data.nodeData.name}`)
			// 标记该模型加载失败（也算完成）
			updateModelProgress(data.nodeData.id, 1.0)
			updateModelNodes()

			// 检查是否所有模型都已加载完成
			checkAllModelsLoaded()
		})

		eventBus.on("model-load-progress", (data) => {
			// console.log(
			// 	`📊 ${data.nodeId} 加载进度: ${Math.round(
			// 		data.progress * 100
			// 	)}%`
			// )
			// 更新单个模型的加载进度
			updateModelProgress(data.nodeId, data.progress)
		})
	}

	// 加载模型数据
	const loadModelData = async () => {
		try {
			loadingText.value = "加载模型数据..."

			const nodes = await ModelDataAdapter.loadFromJSON()

			if (!treeManager) return

			// 设置总模型数量（排除场景根节点）
			totalModelsCount = nodes.filter(
				(node) => node.type !== "scene"
			).length
			// console.log(`📊 总模型数量: ${totalModelsCount}`)

			// 初始化所有模型的进度为0
			nodes.forEach((node) => {
				treeManager!.addNode(node)
				if (node.type !== "scene") {
					modelLoadProgress.value[node.id] = 0
				}
			})

			updateModelNodes()
			markStageCompleted("modelData")
			// console.log(`✅ 加载了 ${nodes.length} 个模型节点`)
		} catch (error) {
			console.error("❌ 加载模型数据失败:", error)
		}
	}

	// 更新模型节点数据
	const updateModelNodes = () => {
		if (!treeManager) return

		const newNodes = treeManager.getAllNodes()

		if (modelNodes.value.length === 0) {
			modelNodes.value = newNodes
			return
		}

		const existingNodesMap = new Map(
			modelNodes.value.map((node) => [node.id, node])
		)

		modelNodes.value = newNodes.map((newNode) => {
			const existingNode = existingNodesMap.get(newNode.id)
			if (existingNode) {
				return {
					...newNode,
				}
			}
			return newNode
		})

		// console.log(
		// 	`🔄 智能更新模型节点: ${newNodes.length} 个节点，保持UI状态`
		// )
	}

	// 初始化应用
	const initializeApp = async (container?: HTMLElement) => {
		const targetContainer = container || config.container
		if (!targetContainer) return

		try {
			loadingText.value = "初始化地图应用..."
			calculateOverallProgress() // 初始进度为0

			// 创建MapApplication实例
			mapApp = new MapApplication({
				container: targetContainer,
				defaultCenter: config.defaultCenter || [
					114.35962345673624, 30.564406727759334,
				],
				defaultZoom: config.defaultZoom || 18,
				timeSystem: {
			enabled: true,
			latitude: 30.564406727759334,
			longitude: 114.35962345673624,
			timeScale: 60,
			autoUpdate: true,
			updateInterval: 1000
		},
			})

			// 🔧 新增：将mapApp设置为全局引用，供LOD系统使用
			if (typeof window !== "undefined") {
				; (window as any).mapApp = mapApp
				console.log("🌍 设置全局mapApp引用")
			}

			treeManager = mapApp.getTreeManager() as DefaultModelTreeManager

			loadingText.value = "等待地图加载..."

			// 等待地图加载完成
			await mapApp.mapLoadPromise
			markStageCompleted("mapbox")

			loadingText.value = "初始化3D图层..."

			// 添加Three.js图层
			mapApp.addThreeJsLayer()

			// 等待Three.js图层准备完成
			await mapApp.threeLayerReadyPromise
			markStageCompleted("threejs")

			// 初始化3D标注管理器
			if (
				mapApp.scene &&
				mapApp.camera &&
				mapApp.renderer &&
				config.container
			) {
				annotationManager.value = new DefaultAnnotation3DManager(
					mapApp.scene,
					mapApp.camera,
					mapApp.renderer,
					config.container
				)

				const coordinateConverter = new AccurateCoordinateConverter(
					SceneConfig.getMapCenter(),
					SceneConfig.getModelRotation(),
					SceneConfig.getBaseAltitude()
				)
				annotationManager.value.setCoordinateConverter(
					coordinateConverter
				)
			}

			// 初始化LOD管理器
			if (mapApp.scene) {
				lodManager.value = markRaw(new DefaultLODManager(mapApp.scene)) as any

				const layerManager = mapApp.getLayerManager()
				if (
					layerManager &&
					lodManager.value &&
					typeof (lodManager.value as any).setLayerManager ===
					"function"
				) {
					; (lodManager.value as any).setLayerManager(layerManager)
				}

				console.log("✅ LOD管理器已初始化")

				// 添加LOD事件监听
				lodManager.value.on("level-changed", (data: any) => {
					// console.log("🔄 LOD级别变化:", data)
				})

				lodManager.value.on("model-loaded", (data: any) => {
					// console.log("📦 LOD模型加载:", data)
				})

				// 移除LOD的render-update-required事件监听，避免频繁重绘导致地图闪烁
				// LOD系统的可见性变化会通过Three.js的正常渲染循环自动更新
				// lodManager.value.on("render-update-required", (data: any) => {
				// 	if (mapApp) {
				// 		mapApp.repaint()
				// 	}
				// })

				// 监听拆楼模式事件
				if (lodManager.value) {
					lodManager.value.on(
						"exploded-mode-entered",
						(data: any) => {
							console.log("🏗️ 进入拆楼模式:", data)
						}
					)

					lodManager.value.on("exploded-mode-exited", (data: any) => {
						console.log("🏗️ 退出拆楼模式:", data)
					})

					// 监听Mapbox底图可见性变化
					lodManager.value.on(
						"mapbox-visibility-change",
						(data: any) => {
							console.log("🗺️ Mapbox底图可见性变化:", data)
							if (
								mapApp &&
								typeof mapApp.setMapboxVisible === "function"
							) {
								mapApp.setMapboxVisible(data.visible)
							}
						}
					)

					// 监听拆楼模式相机控制变化
					lodManager.value.on(
						"exploded-camera-control",
						(data: any) => {
							console.log("🎥 拆楼模式相机控制变化:", data)
							if (
								mapApp &&
								typeof mapApp.setExplodedCameraMode ===
								"function"
							) {
								mapApp.setExplodedCameraMode(
									data.enabled,
									data.buildingId
								)
							}
						}
					)

					// 监听拆楼模式相机高度调整
					lodManager.value.on(
						"exploded-camera-height-change",
						(data: any) => {
							console.log("🎥 拆楼模式相机高度调整:", data)
							if (
								mapApp &&
								typeof mapApp.adjustCameraHeightInExplodedMode ===
								"function"
							) {
								mapApp.adjustCameraHeightInExplodedMode(
									data.targetHeight,
									data.targetFloor
								)
							}
						}
					)

					// 监听保存相机位置事件
					lodManager.value.on("save-camera-position", (data: any) => {
						console.log("💾 保存相机位置:", data)
						if (
							mapApp &&
							typeof mapApp.saveCameraPosition === "function"
						) {
							mapApp.saveCameraPosition()
						}
					})

					// 监听恢复相机位置事件
					lodManager.value.on(
						"restore-camera-position",
						(data: any) => {
							console.log("🔄 恢复相机位置:", data)
							if (
								mapApp &&
								typeof mapApp.restoreCameraPosition ===
								"function"
							) {
								mapApp.restoreCameraPosition()
							}
						}
					)
				}
			}

			// 监听LayerManager的模型加载事件，自动注册到LOD
			eventBus.on("model-loaded", (data: any) => {
				const { nodeId, object3D } = data
				if (lodManager && object3D) {
					registerModelToLOD(nodeId, object3D)
				}
			})

			// 加载模型数据
			await loadModelData()

			loadingText.value = "设置事件监听..."

			// 设置点击事件
			mapApp.addClickCallback((event) => {
				console.log(event)
				if (event.world) {
					clickInfo.value = {
						x: event.world.x,
						y: event.world.y,
						z: event.world.z,
					}

					// 处理3D标注的点击交互
					if (annotationManager.value && mapApp?.camera) {
						annotationManager.value.handleClick(
							event.world,
							mapApp.camera
						)
					}
				}
			})

			// 设置渲染回调，用于更新3D标注和LOD
			mapApp.addRenderCallback((camera) => {
				if (annotationManager.value) {
					annotationManager.value.updateCameraPosition(camera)
				}
				if (lodManager.value) {
					// 减少LOD更新频率，避免过度重绘
					lodManager.value.updateLOD(camera)
				}
			})

			// 🔧 在开始加载模型前禁用LOD更新
			if (lodManager.value) {
				console.log("⏸️ 暂时禁用LOD更新（模型加载期间）")
				lodManager.value.setEnabled(false)
			}

			// 立即开始加载所有模型
			loadingText.value = "加载建筑和地形模型..."
			console.log("🚀 开始加载所有模型...")

			// 异步加载所有模型，不阻塞UI
			loadAllModelsAsync()
				.then(() => {
					console.log("✅ 所有模型加载任务已启动")
					// 注意：实际的完成检查由事件监听器处理
				})
				.catch((error: any) => {
					console.error("❌ 模型加载启动过程中出现错误:", error)
					// 即使启动失败也重新启用LOD并隐藏加载界面
					if (lodManager.value) {
						lodManager.value.setEnabled(true)
					}
					setTimeout(() => {
						isLoading.value = false
					}, 2000)
				})

			console.log("✅ MapApplication初始化完成")
		} catch (error) {
			console.error("❌ 初始化失败:", error)
			loadingText.value = "初始化失败"
		}
	}

	// 将模型注册到LOD系统
	const registerModelToLOD = (nodeId: string, object3D: THREE.Object3D) => {
		if (!lodManager || !treeManager) return

		const node = treeManager.getNodeById(nodeId)
		if (!node) return

		// 注意：阴影设置已经在 LayerManager.setupShadowsForModel 中处理了

		let realPosition = new THREE.Vector3()

		if (object3D.children.length > 0) {
			const firstChild = object3D.children[0] as THREE.Mesh
			if (firstChild.position.length() !== 0) {
				realPosition = firstChild.position.clone()
			} else if (firstChild?.geometry) {
				if (!firstChild.geometry.boundingSphere) {
					firstChild.geometry.computeBoundingSphere()
				}
				if (firstChild.geometry.boundingSphere) {
					realPosition =
						firstChild.geometry.boundingSphere.center.clone()
				}
			}
		}

		if (realPosition.length() === 0) {
			const box = new THREE.Box3().setFromObject(object3D)
			if (!box.isEmpty()) {
				box.getCenter(realPosition)
			}
		}

		let lodConfig: any = {
			id: nodeId,
			name: nodeId,
			position: realPosition,
			object: object3D,
			priority: 0,
			hasParent: !!node.parentId,
		}

		if (node.type === "building_floor") {
			const floorNode = node as any
			const buildingId = nodeId.split("-")[0]

			lodConfig.isInterior = true
			lodConfig.buildingId = buildingId
			lodConfig.floor = floorNode.floor || 1
			lodConfig.isDefault = floorNode.isDefault || floorNode.floor === 1

			object3D.visible = false
			// console.log(
			// 	`🏢 室内模型注册: ${nodeId}, 建筑ID: ${buildingId}, 楼层: ${lodConfig.floor}`
			// )
		} else if (node.type === "building_exterior") {
			const exteriorNode = node as any
			const buildingId = nodeId.replace("-exterior", "")
			const hasInterior = exteriorNode.hasInterior || false

			lodConfig.isExterior = true
			lodConfig.buildingId = buildingId
			lodConfig.hasInterior = hasInterior
			lodConfig.switchDistance =
				exteriorNode.lodConfig?.switchDistance || 50
			lodConfig.fadeDistance = exteriorNode.lodConfig?.fadeDistance || 10

			// console.log(
			// 	`🏛️ 外立面模型注册: ${nodeId}, 建筑ID: ${buildingId}, 有室内: ${hasInterior}`
			// )
		}

		if (
			lodManager.value &&
			typeof (lodManager.value as any).addModelWithObject === "function"
		) {
			; (lodManager.value as any).addModelWithObject(lodConfig)
			// console.log(`✅ LOD注册成功: ${nodeId}, 类型: ${node.type}`)
		}
	}

	// 加载单个模型
	const loadModel = async (nodeId: string) => {
		if (!mapApp) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager || !treeManager) return

		try {
			console.log(`🔄 开始加载单个模型: ${nodeId}`)

			await layerManager.loadNode(
				nodeId,
				(nodeId: string, progress: number) => {
					// console.log(
					// 	`📊 模型 ${nodeId} 加载进度: ${(progress * 100).toFixed(
					// 		1
					// 	)}%`
					// )
				},
				(
					nodeId: string,
					object3D: THREE.Object3D | null,
					success: boolean,
					errorMessage?: string
				) => {
					if (success && object3D && lodManager.value) {
						// console.log(`✅ 模型 ${nodeId} 加载成功，开始注册LOD`)
						registerModelToLOD(nodeId, object3D)
					} else {
						console.error(
							`❌ 模型 ${nodeId} 加载失败:`,
							errorMessage || "未知错误"
						)
					}
				}
			)
			updateModelNodes()
			// console.log(`🎯 模型 ${nodeId} 加载流程完成`)
		} catch (error) {
			console.error(`❌ 加载模型 ${nodeId} 失败:`, error)
		}
	}

	// 切换可见性
	const toggleVisibility = (nodeId: string) => {
		if (!mapApp) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager || !treeManager) return

		const node = treeManager.getNodeById(nodeId)
		if (!node) {
			console.error(`节点 ${nodeId} 未找到`)
			return
		}

		const oldVisibility = node.visible
		const newVisibility = !oldVisibility

		console.log(
			`切换 ${node.name} 可见性: ${oldVisibility} -> ${newVisibility}`
		)

		layerManager.setNodeVisibility(nodeId, newVisibility)
		updateModelNodes()
		mapApp.repaint()

		const updatedNode = treeManager.getNodeById(nodeId)
		console.log(`更新后状态: ${updatedNode?.visible}`)
	}

	// 切换楼层
	const switchFloor = (buildingId: string, targetFloor: number) => {
		if (!mapApp || !treeManager) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager) return

		console.log(`🎮 UI切换楼层: ${buildingId} → 楼层 ${targetFloor}`)

		// 保存UI状态
		buildingCurrentFloors.value[buildingId] = targetFloor
		console.log(`💾 保存UI状态: ${buildingId} → 楼层 ${targetFloor}`)

		// 更新数据模型
		if (treeManager) {
			treeManager.setBuildingCurrentFloor(buildingId, targetFloor)
		}

		// 通过LOD系统控制楼层显示
		if (
			lodManager.value &&
			typeof (lodManager.value as any).setCurrentFloor === "function"
		) {
			; (lodManager.value as any).setCurrentFloor(buildingId, targetFloor)
			console.log(`✅ 楼层切换通过LOD系统完成`)
		}

		mapApp.repaint()
	}

	// 加载所有模型
	const loadAllModels = async () => {
		if (!mapApp) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager || !treeManager) return

		const allNodes = treeManager
			.getAllNodes()
			.filter((node) => node.loadState === "pending")

		for (const node of allNodes) {
			try {
				await layerManager.loadNode(node.id)
			} catch (error) {
				console.error(`加载 ${node.name} 失败:`, error)
			}
		}

		updateModelNodes()
	}

	// 异步加载所有模型（用于初始化时的自动加载）
	const loadAllModelsAsync = async () => {
		if (!mapApp) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager || !treeManager) return

		const allNodes = treeManager
			.getAllNodes()
			.filter(
				(node) => node.loadState === "pending" && node.type !== "scene"
			)

		// console.log(`📦 准备加载 ${allNodes.length} 个模型`)

		// 使用 Promise.allSettled 来并行加载模型，但限制并发数
		const batchSize = 3 // 每批最多加载3个模型
		const batches = []

		for (let i = 0; i < allNodes.length; i += batchSize) {
			batches.push(allNodes.slice(i, i + batchSize))
		}

		for (const batch of batches) {
			const batchPromises = batch.map(async (node) => {
				try {
					// console.log(`🔄 开始加载模型: ${node.name}`)
					await layerManager.loadNode(
						node.id,
						(_nodeId: string, _progress: number) => {
							// 进度回调已在事件监听器中处理
						},
						(
							_nodeId: string,
							_object3D: THREE.Object3D | null,
							_success: boolean,
							_errorMessage?: string
						) => {
							// 完成回调已在事件监听器中处理
						}
					)
				} catch (error) {
					console.error(`❌ 加载模型 ${node.name} 失败:`, error)
				}
			})

			// 等待当前批次完成再开始下一批次
			await Promise.allSettled(batchPromises)
		}

		updateModelNodes()
		console.log(`✅ 所有模型加载任务完成`)
		eventBus.emit("loadComplete")
	}

	// 隐藏所有模型
	const hideAllModels = () => {
		if (!mapApp || !treeManager) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager) return

		const allNodes = treeManager
			.getAllNodes()
			.filter((node) => node.type !== "scene")

		allNodes.forEach((node) => {
			if (node.visible) {
				layerManager.setNodeVisibility(node.id, false)
			}
		})

		updateModelNodes()
		mapApp.repaint()
	}

	// 显示所有模型
	const showAllModels = () => {
		if (!mapApp || !treeManager) return
		const layerManager = mapApp.getLayerManager()
		if (!layerManager) return

		const allNodes = treeManager
			.getAllNodes()
			.filter((node) => node.type !== "scene")

		allNodes.forEach((node) => {
			if (!node.visible) {
				layerManager.setNodeVisibility(node.id, true)
			}
		})

		updateModelNodes()
		mapApp.repaint()
	}

	// 重置视图
	const resetView = () => {
		if (!mapApp) return
		mapApp.getMap().flyTo({
			center: [114.35962345673624, 30.564406727759334],
			zoom: 18,
			pitch: 0,
			bearing: 0,
		})
	}

	// 处理楼层切换
	const handleFloorChange = (buildingId: string, floor: number) => {
		// console.log(`🏢 楼层切换请求: ${buildingId} → 楼层 ${floor}`)

		if (lodManager.value) {
			if (
				typeof (lodManager.value as any).setCurrentFloor === "function"
			) {
				; (lodManager.value as any).setCurrentFloor(buildingId, floor)
			}

			if (
				mapApp?.camera &&
				typeof lodManager.value.updateLOD === "function"
			) {
				lodManager.value.updateLOD(mapApp.camera)
				// console.log(
				// 	`🔄 楼层切换后触发LOD更新: ${buildingId} → 楼层 ${floor}`
				// )
			}
		}
	}

	// 处理LOD配置变更
	const handleLODConfigChange = (config: {
		switchDistance?: number
		fadeDistance?: number
	}) => {
		console.log(`🔧 LOD配置变更:`, config)

		if (
			lodManager.value &&
			typeof (lodManager.value as any).setDefaultLODConfig === "function"
		) {
			; (lodManager.value as any).setDefaultLODConfig(config)
		}
	}

	// 关闭点击信息
	const closeClickInfo = () => {
		clickInfo.value = null
	}

	// 调试方法
	const textPointList = [
		{ id: '618D6E3E-5425-25F3-FF37-01BC190D7FE9', x: 0, y: 0, floor: 1, area: '开发部-王泽玉', color: "#FFFF00", height: 2.1 },
		{ id: '1510FC76-B629-8779-173C-24EF50BD9BA5', x: 5, y: 0, floor: 1, area: '开发部-黄俊', color: "#000000", height: 2.1 },
		{ id: 'EA931567-82C4-DE26-C4DD-4FAF43755823', x: 1.2, y: 3.6, floor: 1, color: "#FF7F50", area: '开发部-我的位置' },
		{ id: 'C3BCC948-D457-0677-6347-C94303AC1160', x: 6.2, y: 3.6, floor: 1, area: '开发部-沈恒' },
		{ id: '310E5AFD-FB93-FE57-123F-6F288D5F4FE1', x: 1.2, y: 9.6, floor: 1, area: '开发部-姜硕' },
		{ id: '4069AC6C-C39A-D4DC-CB92-422084003459', x: 6.2, y: 9.6, floor: 1, area: '开发部-柳敏' },
		{ id: 'CDA07B43-153F-22DA-1F99-1E736715FF16', x: 6.5, y: 11.2, floor: 1, area: '开发部-康博文', height: 1 },
		{ id: '6E37579A-2C64-A684-4E6E-12BB039634F1', x: 0.5, y: 11.2, floor: 1, area: '开发部-邵成威', height: 1 },
		{ id: '7CA19F6F-3D86-26AE-8B3D-75F32721FE16', x: 1.2, y: 15.6, floor: 1, area: '开发部-黄正威' },
		{ id: 'E4EC14AC-C6F4-9D92-0509-50EBA7ED7BB8', x: 6.2, y: 15.6, floor: 1, area: '开发部-李琦' },

	]
	// 用户位置标记（红色球体）
	const userMesh = markRaw(new THREE.Mesh(
		new THREE.SphereGeometry(0.5, 10, 10),
		new THREE.MeshBasicMaterial({ color: 0xff0000 })
	))

	const initTestBeaconLocation = () => {
		const material = new THREE.MeshBasicMaterial({ color: "#00ff00" })
		textPointList.map((point) => {
			const target = userMesh.clone()
			target.material = point.color ? new THREE.MeshBasicMaterial({ color: point.color }) : material
			target.position.set(point.x, (-7 + (point.height || 3.5)), point.y)
			mapApp?.scene?.add(target)
		})
		mapApp?.scene?.add(userMesh)
		userMesh.visible = false;
	}

	const updateUserLocation = (x: number, y: number, floor: number) => {
		userMesh.visible = true;
		userMesh.position.copy(new THREE.Vector3(x, -6.3, y))
		mapApp?.map.triggerRepaint()
	}



	// 时间系统控制方法
		const getCurrentTime = () => {
			return mapApp?.getCurrentTime() || new Date()
		}

		const setCurrentTime = (time: Date) => {
			mapApp?.setCurrentTime(time)
		}

		const startTimeSystem = () => {
			mapApp?.startTimeSystem()
		}

		const stopTimeSystem = () => {
			mapApp?.stopTimeSystem()
		}

		const setTimeScale = (scale: number) => {
			mapApp?.setTimeScale(scale)
		}

		const getTimeScale = () => {
			return mapApp?.getTimeScale() || 1
		}

		return {
			// 响应式状态
			isLoading,
			loadingText,
			loadingProgress,
			clickInfo,
			mapApp,
			// 计算属性
			modelList,
			buildingList,
			stats,

			// 管理器实例
			annotationManager,
			lodManager,

			// 初始化方法
			initializeApp,
			setupEventListeners,

			// 模型操作方法
			loadModel,
			loadAllModels,
			toggleVisibility,
			hideAllModels,
			showAllModels,

			// 楼层操作方法
			switchFloor,
			handleFloorChange,

			// 视图操作方法
			resetView,

			// LOD操作方法
			handleLODConfigChange,

			// UI交互方法
			closeClickInfo,

			// 时间系统控制方法
			getCurrentTime,
			setCurrentTime,
			startTimeSystem,
			stopTimeSystem,
			setTimeScale,
			getTimeScale,

			// 内部状态（如果需要的话）
			buildingCurrentFloors,
			initTestBeaconLocation,
			updateUserLocation,
			// 清理方法
			dispose: () => {
				lodManager.value?.dispose()
				annotationManager.value?.dispose()
				mapApp?.destroy()
			},
		}
}
