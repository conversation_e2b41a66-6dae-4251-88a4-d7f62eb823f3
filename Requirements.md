Mapbox GL JS 与 Three.js 深度融合技术方案 (V2)
1. 项目目标与核心原则
目标：构建一个高性能、视觉效果逼真的 3D 地图应用。该应用将 Mapbox 强大的地理信息渲染能力与 Three.js 丰富的 3D 对象表现力无缝结合，实现动态光照、实时天气和流畅的 3D 动画效果。

核心原则：

Mapbox 主导渲染：渲染循环的控制权完全交给 Mapbox GL JS。Three.js 作为自定义图层，在 Mapbox 的渲染流程中被动调用。

统一时间源：使用单一的 THREE.Clock 驱动高频动画，确保平滑。

数据驱动视觉：场景的氛围由外部数据（如 suncalc.js 计算结果）驱动，通过统一的控制函数同步更新 Mapbox 和 Three.js 的视觉表现。

解耦更新循环 (性能核心)：将低频的场景状态更新（如光照）与高频的动画渲染分离，避免不必要的重复计算。

2. 核心技术栈与配置
地图引擎: mapbox-gl-js (v2.x 或更高版本)

3D 引擎: three.js

太阳位置计算: suncalc.js

固定坐标: 湖北省博物馆 (东经: 114.3589°, 北纬: 30.5639°)

地图样式: Mapbox Standard 样式 (强烈推荐，内置强大的光照和日夜元素配置能力)

3. 渲染与动画核心机制
3.1. 双循环更新架构
为了兼顾性能与动画流畅度，我们采用“慢速循环”和“快速循环”分离的架构。

慢速循环 (Slow Loop - 用于光照和场景)

触发方式: 使用 setInterval，每小时（或分钟）执行一次。

任务:

获取当前模拟时间。

调用 suncalc.js 计算当前时间的太阳/月亮方位。

根据计算结果，准备好一套完整的光照参数（包括 Mapbox 和 Three.js 的光照颜色、强度、雾效颜色等）。

将这套参数存储在一个全局的状态对象中（如 lightingState）。

快速循环 (Fast Loop - 用于动画和渲染)

触发方式: 由 Mapbox 自定义图层的 render 方法在每一帧调用。

任务:

应用光照: 从全局 lightingState 对象中读取光照参数，并应用到 Three.js 的灯光和 Mapbox 的图层上。这是一个低开销的赋值操作。

更新动画: 从全局 THREE.Clock 获取 deltaTime，更新所有需要平滑运动的物体（如水面 Shader、鱼的 AnimationMixer）。

执行渲染: 调用 renderer.render() 并触发 map.triggerRepaint()。

3.2. 无闪烁的融合渲染流程
初始化 (onAdd 方法): 创建 WebGLRenderer 并设置 autoClear = false。加载所有 3D 资源和光照。

逐帧渲染 (render 方法):

同步 Mapbox 和 Three.js 的相机矩阵。

执行“快速循环”中的任务：应用光照状态 + 更新动画。

4. 动态光照与日夜循环
4.1. 核心思路
创建一个统一的控制函数，例如 updateLightingState()，由“慢速循环”调用，负责计算并更新全局的 lightingState 对象。

4.2. Mapbox 端控制
光照 (light): map.setLight({ intensity, color })

天空 (sky):

map.setPaintProperty('sky', 'sky-atmosphere-sun', [azimuth, polar]) 用于定位太阳/月亮。

夜晚效果: 当 lightPreset 设置为 'night' 时，Standard 样式会自动在天空中渲染出星星。雾效的颜色也应调整为深色，以模拟夜空。

雾效 (fog): map.setFog({ color, range })

4.3. Three.js 端控制
平行光 (DirectionalLight): intensity 和 color 随时间变化。

环境光 (AmbientLight): intensity 和 color 随时间变化，奠定场景整体色调。

5. 架构总结与最佳实践
状态先行：先计算出完整的场景状态（光照、天气），再在渲染循环中应用它。

模块化：将 suncalc 计算、光照状态管理、Three.js 自定义图层等逻辑分离。

性能考量：严格遵守双循环架构，将高计算成本的操作（如 suncalc）放在慢速循环中。

遵循此技术方案，您将能够精确地实现您所描述的，兼具性能和视觉效果的动态场景。