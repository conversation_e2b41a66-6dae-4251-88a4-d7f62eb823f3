import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import { resolve } from "node:path"
//@ts-expect-error 引入H5适配
import pptv, { AcceptedPlugin } from "postcss-px-to-viewport-8-plugin"

import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { VantResolver } from "@vant/auto-import-resolver"
import vueDevTools from 'vite-plugin-vue-devtools'
const load_pptv: AcceptedPlugin = pptv({
	viewportWidth: 375,
	viewportHeight: 724,
	propList: ["*"],
	unitPrecision: 6, // 转换后的精度，即小数点位数
	minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
	replace: true, // 是否转换后直接更换属性值
	exclude: [/node_modules/, /src\/refactored/],
})

// https://vite.dev/config/
export default defineConfig({
	plugins: [
		vue(),
		AutoImport({
			resolvers: [VantResolver()],
		}),
		Components({
			resolvers: [VantResolver()],
		}),
		vueDevTools()
	],
	server: {
		host: "0.0.0.0",
	},
	resolve: {
		alias: {
			"@": resolve(__dirname, "src"),
		},
		dedupe: ['three']
	},
	css: {
		postcss: {
			plugins: [load_pptv],
		},
	},
	build: {
		target: "esnext",
	}
})
