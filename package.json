{"name": "mapviewer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@types/mapbox-gl": "^3.4.1", "@types/node": "^24.0.12", "@types/three": "^0.178.1", "gsap": "^3.13.0", "mapbox-gl": "^3.13.0", "sass-embedded": "^1.89.2", "stats.js": "^0.17.0", "suncalc": "^1.9.0", "three": "^0.178.0", "vant": "^4.9.21", "vue": "^3.5.17"}, "devDependencies": {"@types/suncalc": "^1.9.2", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.3", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^2.2.12"}}